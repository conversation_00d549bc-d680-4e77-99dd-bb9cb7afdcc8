# 文档贡献指南

感谢您对产教融合大楼数字化管理系统文档的关注和贡献！本指南旨在帮助您了解如何参与文档的改进和完善。

## 文档贡献流程

### 1. 发现问题或改进点

您可以通过以下方式发现需要改进的文档：
- 阅读文档时发现错误、过时或不清晰的内容
- 发现缺少重要信息或功能说明
- 收集用户反馈中提到的文档问题

### 2. 创建任务

在决定修改文档前，请先：
- 在项目任务管理系统中查找是否已有相关任务
- 如果没有，创建新的文档改进任务，清晰描述问题和改进建议

### 3. 获取最新文档

确保您拥有最新版本的文档：
```bash
git pull origin main
```

### 4. 修改文档

修改文档时，请遵循以下规则：
- 使用Markdown语法编写
- 遵循[文档规范](#文档规范)
- 保持文档结构一致性
- 确保更新后的内容准确、清晰

### 5. 提交变更

完成修改后，提交您的变更：
```bash
git add docs/修改的文件.md
git commit -m "文档: 改进了XXX的描述"
git push
```

### 6. 提交审核

创建合并请求（Pull Request）并等待审核。您的文档变更将由文档维护团队进行审核，确保符合项目标准和质量要求。

## 文档规范

### Markdown语法规范

- 使用标准Markdown语法
- 标题使用层级结构：`#` (H1) → `##` (H2) → `###` (H3)
- 代码块使用三个反引号（```）并标明语言类型
- 列表项使用`-`或`1.`开头
- 链接使用`[文本](URL)`格式
- 图片使用`![替代文本](图片路径)`格式

### 文档内容规范

- **准确性**：确保内容准确无误，特别是技术细节、配置参数和步骤说明
- **完整性**：包含必要的上下文信息，确保读者能够理解和应用
- **简洁性**：使用简洁明了的语言，避免冗余和重复
- **一致性**：保持术语和格式的一致性，遵循[术语表](GLOSSARY.md)

### 文档结构规范

- 每个文档应包含标题、简介、主要内容和结尾
- 复杂主题应分解为易于理解的小节
- 使用列表和表格组织结构化信息
- 相关概念应通过链接相互关联

## 文档审核标准

您的文档贡献将根据以下标准进行审核：

1. **技术准确性**：内容是否技术上准确无误
2. **完整性**：是否包含必要的信息和上下文
3. **清晰度**：说明是否清晰易懂
4. **一致性**：是否与现有文档风格和术语保持一致
5. **格式**：是否符合Markdown格式和项目文档规范

## 文档改进建议

如果您有关于如何改进整体文档的建议，但不想直接修改文档，可以：

- 在项目任务管理系统中创建新的文档改进建议
- 通过邮件向文档维护团队提出建议：<EMAIL>

## 常见问题

### 如何添加新的文档？

1. 确定文档类型和位置
2. 遵循文件命名规范创建新文件
3. 使用模板开始编写（参见 templates 目录）
4. 遵循文档规范完成内容
5. 提交审核

### 如何更新术语表？

1. 编辑 `GLOSSARY.md` 文件
2. 按字母顺序添加新术语或更新现有术语
3. 提供准确的定义和必要的上下文
4. 提交审核

### 如何添加图片？

1. 将图片文件添加到 `docs/images` 目录下的适当子目录
2. 使用有意义的文件名，格式为：`[模块]-[功能]-[类型].png`
3. 在文档中使用相对路径引用图片：`![描述](../images/子目录/图片文件名.png)`

---
最后更新：2024-05-21 