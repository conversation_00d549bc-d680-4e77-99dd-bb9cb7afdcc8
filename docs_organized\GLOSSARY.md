# 术语表

本文档提供产教融合大楼数字化管理系统中使用的专业术语解释，帮助开发人员和用户理解系统中的专业概念。

## 系统术语

### 产教融合
产教融合是指产业与教育融合发展，通过校企合作、工学结合等方式，实现教育链、人才链与产业链、创新链的有机衔接。

### 数字化管理
利用数字技术对物理空间、设备和资源进行管理，通过数据采集、分析和可视化提升管理效率和决策质量。

### 智慧教室
配备了先进数字化设备和智能系统的教室，能够实现环境智能调节、设备智能控制、教学互动和数据采集等功能。

### 设备监控
通过传感器和网络技术实时采集设备运行数据，监控设备状态，预警异常情况的功能模块。

### 预约系统
允许用户在线预约教室、设备等资源的系统，包括预约申请、审批、使用和评价等全流程管理。

## 技术术语

### MVC
Model-View-Controller，一种软件设计模式，将应用程序的数据、用户界面和控制逻辑分离，实现代码的模块化和可维护性。

### DAO
Data Access Object，数据访问对象，一种设计模式，用于分离数据访问逻辑和业务逻辑，提供对数据源的抽象访问。

### JSP
JavaServer Pages，一种动态网页技术，允许开发者在HTML页面中嵌入Java代码，实现动态内容生成。

### JDBC
Java Database Connectivity，Java数据库连接，一种用于执行SQL语句的Java API，可以实现Java程序与各种数据库的连接。

### RESTful API
一种基于HTTP协议的API设计风格，使用HTTP方法（GET、POST、PUT、DELETE等）对资源进行操作，具有简单、灵活、可扩展等特点。

## 业务术语

### 设备状态
- **正常**：设备运行正常，可以正常使用
- **故障**：设备发生故障，无法正常使用
- **维护中**：设备正在进行维护，暂时不可用
- **离线**：设备与系统断开连接，无法监控和控制

### 预约状态
- **待审核**：用户提交了预约申请，等待管理员审核
- **已批准**：管理员批准了预约申请，可以按预约时间使用
- **已拒绝**：管理员拒绝了预约申请，无法使用
- **已完成**：预约的资源已被使用，预约流程结束
- **已取消**：用户取消了预约申请

### 用户角色
- **系统管理员**：具有系统最高权限，可以管理所有功能和数据
- **设备管理员**：负责设备管理和维护的用户
- **教室管理员**：负责教室管理和预约审批的用户
- **普通用户**：可以预约和使用教室、设备的用户

### 告警级别
- **信息**：普通信息通知，不影响系统运行
- **警告**：系统出现异常情况，但不影响主要功能
- **错误**：系统出现错误，部分功能无法正常使用
- **严重**：系统出现严重错误，主要功能无法使用

## 设备类型

### 控制设备
- **中央控制器**：教室控制系统的核心设备，管理和协调各子系统
- **环境控制器**：控制空调、照明等环境设备的控制器
- **多媒体控制器**：控制投影、音响等多媒体设备的控制器

### 感知设备
- **温湿度传感器**：监测教室温度和湿度的设备
- **光照传感器**：监测教室光照强度的设备
- **人体感应器**：检测教室是否有人的设备

### 多媒体设备
- **投影仪**：投射视频画面的设备
- **电子白板**：支持电子书写和交互的白板设备
- **音频系统**：播放声音的设备，包括扬声器、麦克风等

### 摄像设备
- **监控摄像头**：用于安全监控的摄像设备
- **教学摄像机**：用于教学录制和直播的摄像设备
- **AI摄像头**：具有人脸识别、行为分析等AI功能的摄像设备

---
最后更新：2024-05-21 