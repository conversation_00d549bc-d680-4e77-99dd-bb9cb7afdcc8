# EduFusionCenter 项目文档

本目录包含 EduFusionCenter 项目的所有文档资料，经过重新整理和分类。

## 文档结构

文档按以下结构进行了整理：

```
docs_organized/
├── api/               # API相关文档
│   ├── javadoc/       # Java API文档
│   └── rest-api/      # REST API文档
├── development/       # 开发相关文档
│   ├── 01-开发指南.md
│   └── 02-补充开发指南.md
├── guides/            # 使用指南
│   ├── 01-使用手册.md
│   └── 02-功能实现示例.md
├── system/            # 系统设计文档
│   ├── 01-系统功能设计.md
│   ├── 02-系统架构.md
│   ├── 03-项目概述.md
│   └── 04-详细设计.md
├── CHANGELOG.md       # 变更日志
├── CONTRIBUTING.md    # 贡献指南
├── GLOSSARY.md        # 术语表
├── index.md           # 文档索引
└── README.md          # 本文件
```

## 快速导航

- 系统概述和设计请查看 [system/](system/) 目录
- 开发指南请查看 [development/](development/) 目录
- 使用手册请查看 [guides/](guides/) 目录
- API文档请查看 [api/](api/) 目录

更详细的导航，请参考 [index.md](index.md) 文件。

## 文档维护

所有文档应遵循以下规范：

1. 文档文件名使用规范的编号前缀（如：01-文档名称.md）
2. 文档内容使用Markdown格式编写
3. 图片等资源应放置在对应文档目录下的assets文件夹中
4. 对文档的修改应在CHANGELOG.md中记录

最后更新：2025-05-27 