# 开发指南

## 文档信息
- 文档版本：2.0.0
- 创建日期：2024-03-12
- 最后更新：2024-03-12
- 作者：开发团队

## 修订历史
| 版本号 | 修订日期 | 修订人 | 修订说明 |
|-------|---------|--------|----------|
| 2.0.0 | 2024-03-12 | 开发团队 | 整合开发规范、项目结构和API接口文档 |

## 第一部分：开发规范

### 1. 代码规范
```mermaid
mindmap
  root((代码规范))
    命名规范
      驼峰命名
      类名首字母大写
      方法名动词开头
    注释规范
      类注释
      方法注释
      关键代码注释
    代码格式
      缩进4空格
      大括号换行
      空行规范
    异常处理
      统一处理
      分类处理
      日志记录
```

### 2. Git规范
```mermaid
gitGraph
    commit id: "init"
    branch develop
    checkout develop
    commit id: "feature1"
    branch feature/login
    checkout feature/login
    commit id: "login-dev"
    commit id: "login-test"
    checkout develop
    merge feature/login
    branch release/v1.0
    checkout release/v1.0
    commit id: "v1.0-rc"
    checkout main
    merge release/v1.0 tag: "v1.0"
```

### 3. 项目规范
- **命名规范**
  - 包名：com.building.模块名
  - 类名：大驼峰命名
  - 方法名：小驼峰命名
  - 常量：全大写下划线分隔

- **注释规范**
  ```java
  /**
   * 类注释示例
   * <AUTHOR>
   * @version 1.0.0
   */
  public class Example {
      /**
       * 方法注释示例
       * @param param 参数说明
       * @return 返回值说明
       * @throws Exception 异常说明
       */
      public String method(String param) throws Exception {
          // 单行注释示例
          return param;
      }
  }
  ```

- **日志规范**
  ```java
  // 使用SLF4J日志框架
  private static final Logger logger = LoggerFactory.getLogger(Example.class);
  
  // 日志级别使用规范
  logger.debug("调试信息");
  logger.info("一般信息");
  logger.warn("警告信息");
  logger.error("错误信息", e);
  ```

## 第二部分：项目结构

### 1. 目录结构
```
src/
├── main/
│   ├── java/
│   │   └── com.building/
│   │       ├── controller/     # 控制器层
│   │       │   └── servlet/    # Servlet实现
│   │       ├── service/        # 业务逻辑层
│   │       │   ├── impl/      # 接口实现
│   │       │   └── dto/       # 数据传输对象
│   │       ├── dao/           # 数据访问层
│   │       │   └── impl/      # 接口实现
│   │       ├── model/         # 实体类
│   │       ├── util/          # 工具类
│   │       └── config/        # 配置类
│   ├── resources/             # 配置文件
│   │   ├── config/           # 应用配置
│   │   └── sql/              # SQL脚本
│   └── webapp/               # Web资源
│       ├── static/           # 静态资源
│       │   ├── css/         # 样式文件
│       │   ├── js/          # JavaScript文件
│       │   └── images/      # 图片资源
│       ├── WEB-INF/
│       │   ├── views/       # JSP页面
│       │   │   ├── common/  # 公共组件
│       │   │   ├── room/    # 房间管理页面
│       │   │   └── user/    # 用户管理页面
│       │   └── web.xml      # Web配置文件
│       └── index.jsp        # 首页
└── test/                    # 测试代码
    └── java/
        └── com.building/
            ├── controller/  # 控制器测试
            ├── service/     # 服务测试
            └── dao/         # DAO测试
```

### 2. 分层架构
```mermaid
graph TD
    A[表示层] --> B[控制层]
    B --> C[业务层]
    C --> D[数据访问层]
    D --> E[数据库]
    
    A --> A1[JSP页面]
    A --> A2[静态资源]
    
    B --> B1[Servlet]
    B --> B2[Filter]
    
    C --> C1[Service接口]
    C --> C2[Service实现]
    
    D --> D1[DAO接口]
    D --> D2[DAO实现]
```

## 第三部分：API接口文档

### 1. 接口规范

#### 1.1 请求处理流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as Servlet
    participant V as Service
    participant D as 数据库
    
    C->>S: 1. HTTP请求
    S->>S: 2. 会话检查
    S->>V: 3. 调用服务
    V->>D: 4. 数据操作
    D-->>V: 5. 返回结果
    V-->>S: 6. 返回数据
    S-->>C: 7. 页面响应
```

#### 1.2 响应处理
- 成功：转发到对应的JSP页面
- 失败：返回错误信息到原页面
- 未登录：重定向到登录页面

### 2. 核心接口说明

#### 2.1 用户认证接口
- **URL**: `/login`
- **方法**: POST
- **功能**: 用户登录认证
- **参数**:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "captcha": "验证码",
    "remember": "记住登录（可选）"
  }
  ```
- **处理流程**:
  1. 验证码校验
  2. 用户名密码验证
  3. 创建用户会话
  4. 重定向到主页

#### 2.2 房间管理接口
- **URL**: `/room/list`
- **方法**: GET
- **功能**: 获取房间列表和统计信息
- **权限**: 需要登录
- **参数**:
  ```json
  {
    "page": "页码",
    "size": "每页数量",
    "type": "房间类型（可选）",
    "status": "房间状态（可选）"
  }
  ```
- **返回示例**:
  ```json
  {
    "code": 200,
    "message": "success",
    "data": {
      "total": 100,
      "list": [
        {
          "id": 1,
          "roomNumber": "101",
          "type": "会议室",
          "status": "空闲",
          "area": 50.0,
          "capacity": 20
        }
      ]
    }
  }
  ```

### 3. 错误码说明
| 错误码 | 说明 | 处理建议 |
|-------|------|---------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未登录 | 跳转登录页面 |
| 403 | 权限不足 | 申请权限 |
| 500 | 服务器错误 | 联系管理员 |

### 4. 开发工具配置

#### 4.1 IDE配置
- Eclipse/IDEA
  - 编码：UTF-8
  - JDK版本：1.8
  - 代码格式化模板
  - 编辑器配置

#### 4.2 Maven配置
```xml
<properties>
    <java.version>1.8</java.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <servlet.version>4.0.1</servlet.version>
    <jsp.version>2.3.3</jsp.version>
    <jstl.version>1.2</jstl.version>
</properties>

<dependencies>
    <!-- Servlet -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>${servlet.version}</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- JSP -->
    <dependency>
        <groupId>javax.servlet.jsp</groupId>
        <artifactId>javax.servlet.jsp-api</artifactId>
        <version>${jsp.version}</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- JSTL -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>jstl</artifactId>
        <version>${jstl.version}</version>
    </dependency>
</dependencies>
```

### 5. 开发流程

#### 5.1 功能开发流程
```mermaid
graph TD
    A[需求分析] --> B[设计]
    B --> C[编码]
    C --> D[单元测试]
    D --> E[代码审查]
    E --> F[提交代码]
    F --> G[集成测试]
    G --> H[发布]
```

#### 5.2 版本发布流程
```mermaid
graph LR
    A[功能开发] --> B[代码审查]
    B --> C[测试验证]
    C --> D[预发布]
    D --> E[灰度发布]
    E --> F[全量发布]
```

---
最后更新：2024-03-12 