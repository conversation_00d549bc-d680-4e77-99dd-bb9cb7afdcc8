## 控制面板开发指南

### 1. 开发环境配置
#### 1.1 前端依赖
```html
<!-- Bootstrap 5 -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- 自定义样式 -->
<link href="css/dashboard.css" rel="stylesheet">
```

#### 1.2 后端依赖
```xml
<!-- Servlet API -->
<dependency>
    <groupId>javax.servlet</groupId>
    <artifactId>javax.servlet-api</artifactId>
    <version>4.0.1</version>
    <scope>provided</scope>
</dependency>

<!-- JSON处理 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.13.0</version>
</dependency>
```

### 2. 开发规范
#### 2.1 代码组织
- 遵循MVC架构模式
- 使用DAO模式访问数据库
- 统一异常处理
- 规范化API响应格式

#### 2.2 命名规范
- 类名：使用大驼峰命名法（如：`RoomStatsServlet`）
- 方法名：使用小驼峰命名法（如：`getAllRooms`）
- 变量名：使用小驼峰命名法（如：`roomDao`）
- 常量名：使用全大写下划线分隔（如：`MAX_ROOMS`）

#### 2.3 注释规范
```java
/**
 * 类注释示例
 * <AUTHOR>
 * @version 1.0
 */
public class RoomStatsServlet extends HttpServlet {
    /**
     * 方法注释示例
     * @param request HTTP请求对象
     * @param response HTTP响应对象
     * @throws ServletException Servlet异常
     * @throws IOException IO异常
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 单行注释示例
    }
}
```

### 3. 调试指南
#### 3.1 前端调试
- 使用浏览器开发者工具
- 检查网络请求
- 查看控制台输出
- 使用断点调试JavaScript

#### 3.2 后端调试
- 使用IDE调试器
- 添加日志输出
- 检查数据库连接
- 验证API响应

### 4. 测试指南
#### 4.1 单元测试
```java
@Test
public void testGetRoomStats() {
    RoomDao roomDao = new RoomDao();
    Map<String, Integer> stats = roomDao.getRoomStats();
    assertNotNull(stats);
    assertTrue(stats.containsKey("totalRooms"));
}
```

#### 4.2 集成测试
- 测试数据库连接
- 测试API接口
- 测试前端展示
- 测试数据更新

### 5. 部署指南
#### 5.1 环境要求
- JDK 8+
- Tomcat 9+
- MySQL 8.0+
- 现代浏览器支持

#### 5.2 部署步骤
1. 编译项目
2. 配置数据库
3. 部署到Tomcat
4. 启动服务
5. 验证功能 