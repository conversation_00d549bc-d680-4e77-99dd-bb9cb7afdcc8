# 项目结构分析与部署指南

## 📋 项目概述

EduFusionCenter是一个基于Spring MVC的产教融合大楼管理系统，主要功能包括楼层布局展示、设备管理、预约系统等。项目采用现代化的Web技术栈，提供直观的用户界面和完善的后台管理功能。

## 🏗️ 项目基本信息

| 属性 | 值 |
|------|-----|
| **项目名称** | EduFusionCenter |
| **项目类型** | Maven Web应用 (WAR包) |
| **Java版本** | JDK 1.8 |
| **主要框架** | Spring MVC + JSP + Servlet |
| **数据库** | MySQL 8.0 |
| **构建工具** | Maven |
| **最终包名** | EduFusionCenter.war |
| **部署路径** | `/EduFusionCenter` |

## 📁 项目目录结构

```
EduFusionCenter/
├── src/main/
│   ├── java/com/building/          # Java源代码
│   │   ├── controller/             # Spring MVC控制器
│   │   │   └── FloorLayoutController.java  # 楼层布局控制器
│   │   ├── service/               # 业务逻辑层
│   │   ├── dao/                   # 数据访问层
│   │   ├── model/                 # 数据模型
│   │   ├── config/                # 配置类
│   │   ├── filter/                # 过滤器
│   │   ├── listener/              # 监听器
│   │   ├── servlet/               # Servlet类
│   │   └── util/                  # 工具类
│   ├── webapp/                    # Web资源
│   │   ├── WEB-INF/
│   │   │   ├── views/             # JSP视图文件
│   │   │   │   └── layout/        # 布局相关视图
│   │   │   │       └── floor1.jsp # 楼层布局页面
│   │   │   ├── web.xml            # Web配置
│   │   │   ├── spring-mvc.xml     # Spring MVC配置
│   │   │   └── lib/               # 库文件
│   │   ├── static/                # 静态资源
│   │   │   ├── css/               # 样式文件
│   │   │   ├── js/                # JavaScript文件
│   │   │   └── images/            # 图片资源
│   │   ├── META-INF/              # 元数据
│   │   ├── main.jsp               # 主页面
│   │   ├── login.jsp              # 登录页面
│   │   └── floor-layout.jsp       # 独立楼层布局页面
│   └── resources/                 # 资源文件
├── sql/                           # 数据库脚本
│   ├── 01_create_database.sql     # 数据库创建
│   ├── 02_create_tables.sql       # 基础表结构
│   ├── 03_insert_initial_data.sql # 初始数据
│   ├── 04_create_device_table.sql # 设备管理表
│   ├── 05_create_reservation_table.sql # 预约系统表
│   ├── 06_create_production_line_table.sql # 生产线表
│   ├── 07_create_system_settings.sql # 系统设置表
│   ├── 08_create_backup_logs.sql  # 备份日志表
│   ├── 09_create_backup_schedule.sql # 备份计划表
│   ├── 10_update_room_table.sql   # 房间表更新
│   ├── 11_update_device_table.sql # 设备表更新
│   └── 12_create_camera_table.sql # 摄像头监控表
├── docs_organized/                # 项目文档
│   ├── development/               # 开发文档
│   ├── api/                       # API文档
│   ├── guides/                    # 使用指南
│   ├── system/                    # 系统文档
│   └── 楼层布局图功能说明.md      # 楼层布局功能说明
├── target/                        # Maven构建输出
├── build/                         # 构建相关文件
├── download/                      # 下载文件
├── pom.xml                        # Maven配置
├── .project                       # Eclipse项目配置
├── .classpath                     # Eclipse类路径配置
└── .settings/                     # Eclipse设置
```

## 🔧 技术栈详细分析

### 后端技术栈
- **Spring Framework 5.3.10**
  - Spring MVC: Web层框架
  - Spring Context: 依赖注入容器
  - Spring Beans: Bean管理
  - Spring Core: 核心功能

- **Java EE组件**
  - Servlet API 4.0.1: Web容器支持
  - JSP API 2.3.3: 页面模板技术
  - JSTL 1.2: JSP标准标签库

- **数据库相关**
  - MySQL Connector 8.0.28: MySQL数据库驱动
  - 支持连接池和事务管理

- **JSON处理**
  - Jackson 2.15.2: JSON序列化/反序列化
  - Gson 2.8.9: Google JSON库

- **日志系统**
  - SLF4J 1.7.32: 日志门面
  - Logback 1.2.6: 日志实现

- **邮件功能**
  - JavaMail 1.6.2: 邮件发送支持

### 前端技术栈
- **UI框架**: Bootstrap 5.1.3
- **图标库**: Font Awesome 6.0.0
- **JavaScript**: 原生ES6+
- **CSS**: 现代CSS3特性，包括Grid、Flexbox、渐变等
- **响应式设计**: 支持多种屏幕尺寸

## 🗄️ 数据库设计

### 数据库脚本执行顺序
1. `01_create_database.sql` - 创建数据库
2. `02_create_tables.sql` - 创建基础表结构
3. `03_insert_initial_data.sql` - 插入初始数据
4. `04_create_device_table.sql` - 设备管理表
5. `05_create_reservation_table.sql` - 预约系统表
6. `06_create_production_line_table.sql` - 生产线表
7. `07_create_system_settings.sql` - 系统设置表
8. `08_create_backup_logs.sql` - 备份日志表
9. `09_create_backup_schedule.sql` - 备份计划表
10. `10_update_room_table.sql` - 房间表更新
11. `11_update_device_table.sql` - 设备表更新
12. `12_create_camera_table.sql` - 摄像头监控表

### 主要数据表
- **用户管理**: 用户信息、权限管理
- **房间管理**: 房间信息、类型分类
- **设备管理**: 设备信息、状态监控
- **预约系统**: 预约记录、时间管理
- **监控系统**: 摄像头信息、监控记录
- **系统设置**: 配置参数、备份管理

## 🎨 核心功能特性

### 楼层布局系统
- **纯HTML/CSS实现**: 不依赖图片文件，完全由代码生成
- **响应式设计**: 支持不同屏幕尺寸自适应
- **交互式界面**: 点击房间查看详细信息
- **房间类型分类**: 
  - 研发中心 (橙色系)
  - 教室 (蓝色系)
  - 实验室 (紫色系)
  - 办公室 (绿色系)
  - 会议室 (橙黄色系)
  - 多媒体教室 (粉色系)
  - 公共设施 (青色系)
  - 通道 (棕色系)

### 建筑结构元素
- **外墙**: 深色边框围绕整个平面图
- **内墙**: 分隔不同区域的墙体
- **走廊**: 中央通道区域
- **楼梯**: 斜纹图案表示
- **电梯**: 特殊样式标识

## 🚀 部署指南

### 服务器环境要求

#### 必需软件
- **JDK**: 1.8 或更高版本
- **Web服务器**: Apache Tomcat 8.5+ 或 9.x
- **数据库**: MySQL 8.0+
- **构建工具**: Maven 3.6+ (用于构建)

#### 推荐配置
- **内存**: 4GB RAM 或更高
- **存储**: 10GB 可用空间
- **网络**: 稳定的网络连接

### 部署步骤

#### 1. 环境准备
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查MySQL服务状态
systemctl status mysql
```

#### 2. 项目构建
```bash
# 进入项目目录
cd /path/to/EduFusionCenter

# 清理并构建项目
mvn clean package

# 构建成功后，WAR包位于 target/EduFusionCenter.war
```

#### 3. 数据库初始化
```sql
-- 1. 创建数据库用户（如需要）
CREATE USER 'edufusion'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON edufusion_center.* TO 'edufusion'@'localhost';
FLUSH PRIVILEGES;

-- 2. 按顺序执行SQL脚本
-- 执行 sql/ 目录下的所有脚本，按文件名顺序执行
```

#### 4. 配置文件修改
更新数据库连接配置（通常在配置文件中）：
```properties
# 数据库连接配置示例
db.url=****************************************************************************
db.username=edufusion
db.password=your_password
db.driver=com.mysql.cj.jdbc.Driver
```

#### 5. Tomcat部署
```bash
# 停止Tomcat服务
sudo systemctl stop tomcat

# 复制WAR包到Tomcat webapps目录
sudo cp target/EduFusionCenter.war /opt/tomcat/webapps/

# 启动Tomcat服务
sudo systemctl start tomcat

# 查看部署日志
tail -f /opt/tomcat/logs/catalina.out
```

#### 6. 验证部署
访问以下URL验证部署是否成功：
- 主页: `http://your-server:8080/EduFusionCenter/`
- 登录页: `http://your-server:8080/EduFusionCenter/login.jsp`
- 楼层布局: `http://your-server:8080/EduFusionCenter/floor-layout.jsp`
- Spring MVC楼层布局: `http://your-server:8080/EduFusionCenter/layout/floor1`

### 常见问题排查

#### 1. 数据库连接问题
- 检查MySQL服务是否运行
- 验证数据库用户权限
- 确认连接字符串正确

#### 2. WAR包部署问题
- 检查Tomcat日志文件
- 验证WAR包完整性
- 确认Tomcat版本兼容性

#### 3. 页面访问问题
- 检查防火墙设置
- 验证端口是否开放
- 确认应用上下文路径

## 📊 性能优化建议

### 数据库优化
- 为常用查询字段添加索引
- 定期清理日志表数据
- 配置数据库连接池

### 应用优化
- 启用Gzip压缩
- 配置静态资源缓存
- 优化图片和CSS文件大小

### 服务器优化
- 调整JVM堆内存大小
- 配置Tomcat连接器参数
- 设置适当的会话超时时间

## 🔒 安全考虑

### 应用安全
- 实施输入验证和SQL注入防护
- 配置HTTPS加密传输
- 设置适当的会话管理

### 数据库安全
- 使用专用数据库用户
- 限制数据库访问权限
- 定期备份数据库

### 服务器安全
- 及时更新系统补丁
- 配置防火墙规则
- 监控系统日志

## 📝 维护指南

### 日常维护
- 监控应用日志
- 检查数据库性能
- 备份重要数据

### 更新部署
- 测试新版本功能
- 备份当前版本
- 平滑升级部署

### 故障处理
- 建立监控告警机制
- 制定故障恢复流程
- 保持技术文档更新

## 📞 技术支持

如需技术支持，请参考以下资源：
- 项目文档: `docs_organized/` 目录
- API文档: `docs_organized/api/` 目录
- 开发指南: `docs_organized/development/` 目录
- 楼层布局功能说明: `docs_organized/楼层布局图功能说明.md`

---

**文档版本**: v1.0  
**最后更新**: 2024年12月  
**维护者**: EduFusionCenter开发团队 