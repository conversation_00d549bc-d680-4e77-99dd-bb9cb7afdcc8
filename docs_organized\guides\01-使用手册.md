# 使用手册

## 文档信息
- 文档版本：2.0.0
- 创建日期：2024-03-12
- 最后更新：2024-03-12
- 作者：产品团队

## 修订历史
| 版本号 | 修订日期 | 修订人 | 修订说明 |
|-------|---------|--------|----------|
| 2.0.0 | 2024-03-12 | 产品团队 | 整合用户指南、功能说明和常见问题 |

## 第一部分：系统概述

### 1. 系统简介
```mermaid
mindmap
  root((产教融合大楼<br>数字化管理系统))
    基础设施管理
      大楼管理
      楼层管理
      房间管理
    教学资源管理
      教室管理
      设备管理
      预约管理
    人员管理
      教师管理
      学生管理
      企业人员
    智能监控
      能耗监控
      环境监控
      安全监控
```

### 2. 用户角色
```mermaid
graph TD
    A[系统用户] --> B[管理员]
    A --> C[教师]
    A --> D[学生]
    A --> E[企业用户]
    
    B --> B1[系统管理员]
    B --> B2[设施管理员]
    
    C --> C1[任课教师]
    C --> C2[实验室管理员]
    
    D --> D1[在校学生]
    D --> D2[实习学生]
    
    E --> E1[企业管理员]
    E --> E2[企业员工]
```

### 3. 系统功能
| 模块 | 主要功能 | 适用角色 |
|------|----------|----------|
| 基础设施 | 房间管理、设备管理 | 管理员 |
| 教学资源 | 教室预约、设备借用 | 全部用户 |
| 人员管理 | 人员信息、权限管理 | 管理员 |
| 智能监控 | 能耗监控、环境监控 | 管理员 |

## 第二部分：快速入门

### 1. 系统登录
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant A as 认证中心
    
    U->>S: 访问系统
    S->>U: 显示登录页面
    U->>S: 输入账号密码
    S->>A: 验证身份
    A-->>S: 返回结果
    S-->>U: 登录成功/失败
```

#### 1.1 登录方式
- 账号密码登录
- 扫码登录（微信/支付宝）
- 单点登录（统一认证）

#### 1.2 密码规则
- 长度：8-20位
- 组成：字母+数字+特殊字符
- 有效期：90天
- 找回方式：邮箱验证

### 2. 界面导航
```mermaid
graph LR
    A[首页] --> B[功能菜单]
    A --> C[快捷操作]
    A --> D[消息中心]
    
    B --> B1[基础设施]
    B --> B2[教学资源]
    B --> B3[人员管理]
    B --> B4[智能监控]
    
    C --> C1[房间预约]
    C --> C2[设备借用]
    C --> C3[通知发布]
    
    D --> D1[系统消息]
    D --> D2[预警信息]
```

### 3. 基本操作
1. **查询操作**
   - 输入关键字
   - 选择筛选条件
   - 点击查询按钮
   - 查看结果列表

2. **新增操作**
   - 点击新增按钮
   - 填写必要信息
   - 点击保存按钮
   - 确认提交

3. **修改操作**
   - 选择目标记录
   - 点击编辑按钮
   - 修改相关信息
   - 保存更改

4. **删除操作**
   - 选择目标记录
   - 点击删除按钮
   - 确认删除操作
   - 完成删除

## 第三部分：功能详解

### 1. 房间管理
#### 1.1 房间预约流程
```mermaid
graph TD
    A[查看房间] --> B[选择时间]
    B --> C[填写用途]
    C --> D[提交申请]
    D --> E{审核}
    E -->|通过| F[预约成功]
    E -->|拒绝| G[预约失败]
```

#### 1.2 房间状态说明
| 状态 | 说明 | 颜色标识 |
|------|------|----------|
| 空闲 | 可以预约 | 绿色 |
| 使用中 | 正在使用 | 红色 |
| 维护中 | 不可使用 | 灰色 |
| 已预约 | 已被预定 | 黄色 |

### 2. 设备管理
#### 2.1 设备借用流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant A as 管理员
    
    U->>S: 查看设备列表
    U->>S: 选择设备
    U->>S: 提交借用申请
    S->>A: 通知管理员
    A->>S: 审核申请
    S-->>U: 通知结果
```

#### 2.2 设备状态说明
| 状态 | 说明 | 操作建议 |
|------|------|----------|
| 可用 | 设备正常 | 可直接借用 |
| 借出 | 已被借用 | 查看归还时间 |
| 维修 | 设备故障 | 等待维修完成 |
| 报废 | 设备报废 | 不可借用 |

### 3. 智能监控
#### 3.1 能耗监控
```mermaid
graph TD
    A[能耗监控] --> B[用电监控]
    A --> C[用水监控]
    A --> D[空调监控]
    
    B --> B1[实时数据]
    B --> B2[统计分析]
    
    C --> C1[实时数据]
    C --> C2[统计分析]
    
    D --> D1[实时数据]
    D --> D2[统计分析]
```

#### 3.2 环境监控
| 监控项 | 正常范围 | 预警值 |
|--------|----------|--------|
| 温度 | 18-26℃ | <16℃或>28℃ |
| 湿度 | 40-60% | <30%或>70% |
| CO2 | <1000ppm | >1500ppm |
| PM2.5 | <75μg/m³ | >150μg/m³ |

## 第四部分：常见问题

### 1. 账号相关
#### 1.1 无法登录
1. 检查账号密码是否正确
2. 确认网络连接是否正常
3. 清除浏览器缓存后重试
4. 联系管理员重置密码

#### 1.2 密码修改
1. 登录系统后进入个人中心
2. 点击"修改密码"按钮
3. 输入原密码和新密码
4. 确认修改完成

### 2. 预约相关
#### 2.1 预约失败
1. 检查时间是否冲突
2. 确认是否有预约权限
3. 查看房间是否可用
4. 联系管理员协助

#### 2.2 取消预约
1. 进入预约记录
2. 选择需要取消的预约
3. 点击取消按钮
4. 填写取消原因

### 3. 系统使用
#### 3.1 操作技巧
```mermaid
mindmap
  root((操作技巧))
    快捷键
      Ctrl+F：搜索
      Ctrl+S：保存
      Esc：关闭
    批量操作
      多选：Ctrl+点击
      全选：Ctrl+A
      导出：Excel格式
    数据筛选
      组合条件
      高级搜索
      自定义筛选
```

#### 3.2 注意事项
1. **数据安全**
   - 定期修改密码
   - 及时退出登录
   - 保护个人信息

2. **操作安全**
   - 谨慎删除操作
   - 保存重要数据
   - 记录操作日志

## 第五部分：移动端使用

### 1. APP使用
#### 1.1 下载安装
1. 扫描二维码下载
2. 应用商店搜索下载
3. 官网直接下载

#### 1.2 功能特点
```mermaid
graph TD
    A[移动端功能] --> B[基础功能]
    A --> C[特色功能]
    A --> D[便捷服务]
    
    B --> B1[房间预约]
    B --> B2[设备借用]
    
    C --> C1[扫码签到]
    C --> C2[移动审批]
    
    D --> D1[消息推送]
    D --> D2[位置服务]
```

### 2. 小程序使用
#### 2.1 访问方式
1. 微信扫码进入
2. 搜索小程序名称
3. 收藏便捷使用

#### 2.2 特色功能
| 功能 | 说明 | 使用场景 |
|------|------|----------|
| 快速预约 | 一键预约房间 | 临时会议 |
| 扫码签到 | 扫码进出房间 | 上课签到 |
| 移动审批 | 随时处理审批 | 加急审批 |
| 实时通知 | 消息即时推送 | 重要通知 |

---
最后更新：2024-03-12 