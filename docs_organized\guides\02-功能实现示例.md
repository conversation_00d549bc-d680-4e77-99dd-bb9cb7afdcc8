# 功能实现示例

本文档记录了系统中已实现的功能模块的技术实现细节，作为开发参考。

## 1. 房间管理模块

### 1.1 功能概述
房间管理模块提供了对大楼内各个房间的管理功能，包括房间列表展示、添加、编辑、删除等操作。

### 1.2 技术实现
- 访问地址：`http://localhost:8080/EduFusionCenter/room/list`
- 实现类：`RoomListServlet`
- 视图文件：`/WEB-INF/views/room/list.jsp`

#### 1.2.1 控制器实现
```java
@WebServlet("/room/list")
public class RoomListServlet extends HttpServlet {
    private RoomDao roomDao;
    
    @Override
    public void init() throws ServletException {
        roomDao = new RoomDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户登录状态
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        try {
            // 获取房间列表
            List<Room> rooms = roomDao.getRooms(1, 10, null);
            int totalRooms = roomDao.getTotalRooms(null);
            
            // 获取房间统计信息
            int occupiedRooms = roomDao.getOccupiedRoomCount();
            int availableRooms = totalRooms - occupiedRooms;
            
            // 设置请求属性
            request.setAttribute("rooms", rooms);
            request.setAttribute("totalRooms", totalRooms);
            request.setAttribute("occupiedRooms", occupiedRooms);
            request.setAttribute("availableRooms", availableRooms);
            
            // 转发到视图
            request.getRequestDispatcher("/WEB-INF/views/room/list.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "获取房间列表失败：" + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }
}
```

#### 1.2.2 数据访问层实现
```java
public class RoomDao {
    public List<Room> getRooms(int page, int pageSize, String search) throws SQLException {
        List<Room> rooms = new ArrayList<>();
        String sql = "SELECT * FROM room WHERE 1=1";
        List<Object> params = new ArrayList<>();
        
        if (search != null && !search.trim().isEmpty()) {
            sql += " AND (room_number LIKE ? OR room_name LIKE ?)";
            params.add("%" + search + "%");
            params.add("%" + search + "%");
        }
        
        sql += " LIMIT ? OFFSET ?";
        params.add(pageSize);
        params.add((page - 1) * pageSize);
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Room room = new Room();
                    room.setId(rs.getInt("id"));
                    room.setRoomNumber(rs.getString("room_number"));
                    room.setRoomName(rs.getString("room_name"));
                    room.setFloor(rs.getInt("floor"));
                    room.setCapacity(rs.getInt("capacity"));
                    room.setStatus(rs.getString("status"));
                    rooms.add(room);
                }
            }
        }
        return rooms;
    }
}
```

## 2. 设备管理模块

### 2.1 功能概述
设备管理模块提供了对大楼内各类设备的管理功能，包括设备列表展示、添加、编辑、删除等操作。

### 2.2 技术实现
- 访问地址：`http://localhost:8080/EduFusionCenter/device/list`
- 实现类：`DeviceListServlet`
- 视图文件：`/WEB-INF/views/device/list.jsp`

#### 2.2.1 控制器实现
```java
@WebServlet("/device/list")
public class DeviceListServlet extends HttpServlet {
    private DeviceDao deviceDao;
    
    @Override
    public void init() throws ServletException {
        deviceDao = new DeviceDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        // 检查用户登录状态
        HttpSession session = request.getSession();
        if (session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        try {
            // 获取设备列表
            List<Device> devices = deviceDao.getDevices(1, 10, null);
            int totalDevices = deviceDao.getTotalDevices(null);
            
            // 获取设备统计信息
            int workingDevices = deviceDao.getWorkingDeviceCount();
            int maintenanceDevices = deviceDao.getMaintenanceDeviceCount();
            
            // 设置请求属性
            request.setAttribute("devices", devices);
            request.setAttribute("totalDevices", totalDevices);
            request.setAttribute("workingDevices", workingDevices);
            request.setAttribute("maintenanceDevices", maintenanceDevices);
            
            // 转发到视图
            request.getRequestDispatcher("/WEB-INF/views/device/list.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "获取设备列表失败：" + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error/500.jsp").forward(request, response);
        }
    }
}
```

#### 2.2.2 数据访问层实现
```java
public class DeviceDao {
    public List<Device> getDevices(int page, int pageSize, String search) throws SQLException {
        List<Device> devices = new ArrayList<>();
        String sql = "SELECT * FROM device WHERE 1=1";
        List<Object> params = new ArrayList<>();
        
        if (search != null && !search.trim().isEmpty()) {
            sql += " AND (device_number LIKE ? OR device_name LIKE ?)";
            params.add("%" + search + "%");
            params.add("%" + search + "%");
        }
        
        sql += " LIMIT ? OFFSET ?";
        params.add(pageSize);
        params.add((page - 1) * pageSize);
        
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < params.size(); i++) {
                stmt.setObject(i + 1, params.get(i));
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Device device = new Device();
                    device.setId(rs.getInt("id"));
                    device.setDeviceNumber(rs.getString("device_number"));
                    device.setDeviceName(rs.getString("device_name"));
                    device.setDeviceType(rs.getString("device_type"));
                    device.setLocation(rs.getString("location"));
                    device.setStatus(rs.getString("status"));
                    device.setLastMaintenanceDate(rs.getDate("last_maintenance_date"));
                    devices.add(device);
                }
            }
        }
        return devices;
    }
}
```

## 3. 实现特点

### 3.1 安全性
- 所有页面都需要用户登录才能访问
- 使用会话（Session）管理用户登录状态
- 使用参数化查询防止SQL注入

### 3.2 性能优化
- 使用分页查询减少数据库压力
- 使用连接池管理数据库连接
- 合理使用索引提高查询效率

### 3.3 用户体验
- 提供搜索功能
- 显示统计信息
- 统一的错误处理
- 响应式布局设计

### 3.4 代码组织
- 采用MVC架构
- 使用DAO模式访问数据库
- 统一的异常处理机制
- 清晰的代码注释

## 4. 后续优化建议

1. 添加缓存机制，减少数据库访问
2. 实现更复杂的搜索和筛选功能
3. 添加数据导出功能
4. 实现批量操作功能
5. 添加操作日志记录
6. 优化页面加载性能
7. 添加数据可视化图表
8. 实现实时数据更新

---
最后更新：2024-04-02 