# 摄像头删除功能说明

## 功能概述

本文档描述了新增的摄像头删除功能，该功能允许用户从系统中永久删除摄像头设备。

## 功能特性

### 1. 删除入口
- **摄像头列表页面**：在每个摄像头卡片的下拉菜单中提供"删除摄像头"选项
- **摄像头详情页面**：在页面顶部提供"删除摄像头"按钮

### 2. 安全确认机制
- **列表页面**：使用Bootstrap模态框进行删除确认，提供详细的警告信息
- **详情页面**：使用JavaScript confirm对话框进行确认

### 3. 删除警告信息
删除确认时会明确告知用户此操作将：
- 永久删除摄像头配置信息
- 停止所有相关的视频流
- 清除相关的历史记录

## 技术实现

### 1. 后端实现

#### CameraDeleteServlet
- **路径**：`/camera/delete`
- **方法**：POST
- **功能**：处理摄像头删除请求
- **参数**：`id` - 摄像头ID
- **返回**：JSON格式的操作结果

```java
@WebServlet("/camera/delete")
public class CameraDeleteServlet extends HttpServlet {
    // 处理删除请求，包含参数验证和错误处理
}
```

#### 服务层
- 使用现有的 `CameraService.deleteCamera(int id)` 方法
- 通过 `CameraDao.deleteCamera(int id)` 执行数据库删除操作

### 2. 前端实现

#### 摄像头列表页面
- 在下拉菜单中添加删除选项
- 使用Bootstrap模态框进行确认
- JavaScript函数：`deleteCamera(cameraId, cameraName)`

#### 摄像头详情页面
- 在页面顶部添加删除按钮
- 使用data属性传递摄像头信息
- 事件监听器处理删除操作

## 用户界面

### 1. 摄像头列表页面
```html
<li><a class="dropdown-item text-danger" href="javascript:void(0)" 
       onclick="deleteCamera(${camera.id}, '${camera.name}')">
    <i class="bi bi-trash me-2"></i>删除摄像头
</a></li>
```

### 2. 摄像头详情页面
```html
<button class="btn btn-outline-danger" id="deleteCameraBtn" 
        data-camera-id="${camera.id}" data-camera-name="${camera.name}">
    <i class="bi bi-trash me-1"></i> 删除摄像头
</button>
```

### 3. 确认模态框
提供详细的警告信息和操作按钮：
- 取消按钮：关闭模态框，不执行删除
- 确认删除按钮：执行删除操作

## 操作流程

### 从列表页面删除
1. 用户点击摄像头卡片的三点菜单
2. 选择"删除摄像头"选项
3. 系统显示确认删除模态框
4. 用户确认删除操作
5. 系统发送删除请求
6. 显示操作结果并刷新页面

### 从详情页面删除
1. 用户在摄像头详情页面点击"删除摄像头"按钮
2. 系统显示确认对话框
3. 用户确认删除操作
4. 系统发送删除请求
5. 删除成功后跳转回摄像头列表页面

## 错误处理

### 1. 参数验证
- 检查摄像头ID是否为空或无效
- 验证摄像头是否存在

### 2. 错误响应
- 参数错误：返回相应的错误信息
- 删除失败：返回操作失败信息
- 系统异常：返回系统错误信息

### 3. 前端错误处理
- 网络错误：显示"删除失败，请稍后重试"
- 服务器错误：显示具体的错误信息

## 安全考虑

1. **确认机制**：双重确认防止误删
2. **参数验证**：后端验证所有输入参数
3. **错误处理**：完善的异常处理机制
4. **用户反馈**：清晰的操作结果提示

## 注意事项

1. **不可撤销**：删除操作是永久性的，无法恢复
2. **关联数据**：删除摄像头会影响相关的视频流和历史记录
3. **权限控制**：建议在实际部署时添加权限验证
4. **数据备份**：重要数据删除前建议进行备份

## 更新记录

- **2025-01-27**：初始版本，实现基本的摄像头删除功能
- 支持从列表页面和详情页面删除摄像头
- 添加安全确认机制和错误处理
