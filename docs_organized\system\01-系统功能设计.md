# 系统功能设计文档

## 文档信息
- 文档版本：1.0.0
- 创建日期：2024-03-10
- 最后更新：2024-03-10
- 作者：系统管理员

## 修订历史
| 版本号 | 修订日期 | 修订人 | 修订说明 |
|-------|---------|--------|----------|
| 1.0.0 | 2024-03-10 | 系统管理员 | 合并功能设计和详细设计文档 |

## 第一部分：功能概述

### 1. 系统功能架构
```mermaid
graph TD
    A[系统功能模块] --> B[资源管理模块]
    A --> C[能耗管理模块]
    A --> D[人员管理模块]
    A --> E[环境管理模块]
    A --> F[服务管理模块]

    B --> B1[教室管理]
    B --> B2[设备管理]
    B --> B3[预约管理]

    C --> C1[能耗监控]
    C --> C2[能耗分析]
    C --> C3[能耗优化]

    D --> D1[人员信息]
    D --> D2[考勤管理]
    D --> D3[访客管理]

    E --> E1[环境监测]
    E --> E2[卫生管理]
    E --> E3[安全管理]

    F --> F1[服务请求]
    F --> F2[投诉处理]
    F --> F3[信息发布]
```

### 2. 核心功能说明

#### 2.1 资源管理模块
- 教室管理
  * 教室信息维护
  * 使用状态监控
  * 预约管理
- 设备管理
  * 设备台账
  * 设备状态监控
  * 维修管理
- 预约管理
  * 在线预约
  * 审批流程
  * 使用反馈

#### 2.2 能耗管理模块
- 能耗监控
  * 实时监控
  * 数据采集
  * 异常告警
- 能耗分析
  * 数据统计
  * 趋势分析
  * 报表生成
- 能耗优化
  * 优化建议
  * 方案制定
  * 效果评估

## 第二部分：详细设计

### 1. 功能模块详细设计

#### 1.1 资源管理模块

##### 1.1.1 教室管理
- 功能描述
- 业务流程
- 数据流转
- 接口设计
- 界面原型

##### 1.1.2 设备管理
- 功能描述
- 业务流程
- 数据流转
- 接口设计
- 界面原型

#### 1.2 能耗管理模块

##### 1.2.1 能耗监控
- 功能描述
- 业务流程
- 数据流转
- 接口设计
- 界面原型

### 2. 业务流程设计

#### 2.1 预约流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant S as 系统
    participant A as 管理员

    U->>S: 提交预约申请
    S->>A: 通知审批
    A->>S: 审批结果
    S->>U: 反馈结果
```

## 第三部分：实现细节

### 1. 技术实现方案

#### 1.1 前端实现
- 技术栈选择
- 组件设计
- 数据交互
- 性能优化

#### 1.2 后端实现
- 架构设计
- 接口实现
- 数据处理
- 性能优化

### 2. 安全性设计
- 身份认证
- 权限控制
- 数据加密
- 日志审计

### 3. 性能优化
- 缓存策略
- 并发处理
- 负载均衡
- 数据库优化

## 3.4 主页布局设计

### 3.4.1 整体布局结构

主页采用现代化的响应式布局设计，主要分为三个核心区域：

1. **顶部导航栏**（固定位置）
2. **左侧边栏**（固定位置）
3. **主要内容区域**（自适应宽度）

```ascii
+------------------+------------------------------------+
|    导航栏        |                                    |
+------------------+------------------------------------+
|                  |                                    |
|                  |                                    |
|                  |                                    |
|    侧边栏        |          主要内容区域              |
|                  |                                    |
|                  |                                    |
|                  |                                    |
+------------------+------------------------------------+
```

### 3.4.2 组件详细说明

#### 1. 顶部导航栏
- **位置**：固定在页面顶部
- **高度**：48px
- **背景色**：深色主题（bg-dark）
- **层级**：z-index: 1030（确保始终在最上层）
- **组件构成**：
  - 左侧：系统名称（产教融合大楼数字化管理系统）
  - 右侧：用户欢迎信息和退出按钮
- **样式特点**：
  ```css
  .navbar {
      height: 48px;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 1030;
  }
  ```

#### 2. 左侧边栏
- **宽度**：240px
- **位置**：固定在左侧，从导航栏下方开始（top: 48px）
- **内容**：导航菜单
- **层级**：z-index: 100
- **菜单项**：
  | 图标 | 菜单名称 | 链接 |
  |------|---------|------|
  | 🏠 | 首页 | main.jsp |
  | 🏢 | 房间管理 | room/list |
  | 👥 | 用户管理 | user/list |
  | ⚙️ | 系统设置 | system/settings |

- **样式特点**：
  ```css
  .sidebar {
      width: 240px;
      position: fixed;
      top: 48px;
      height: calc(100vh - 48px);
      z-index: 100;
  }
  ```

#### 3. 主要内容区域
- **位置**：右侧主区域
- **边距**：
  - 左边距：240px（对应侧边栏宽度）
  - 上边距：48px（对应导航栏高度）
  - 内边距：20px（四周）
- **尺寸**：
  - 宽度：calc(100% - 240px)
  - 最小高度：calc(100vh - 48px)
- **定位**：相对定位（position: relative）
- **内容布局**：
  1. 页面标题：控制面板
  2. 统计卡片区域（3列布局）：
     - 房间总数（蓝色卡片）
     - 已使用房间（绿色卡片）
     - 空闲房间（黄色卡片）

### 3.4.3 响应式设计

- **断点设置**：
  - 中等屏幕（md）：≥768px
  - 大屏幕（lg）：≥992px

- **响应式行为**：
  1. 侧边栏在小屏幕下可折叠
  2. 统计卡片在小屏幕下垂直堆叠
  3. 内容区域自适应调整边距

### 3.4.4 交互效果

1. **卡片悬停效果**：
   ```css
   .card {
       transition: transform 0.2s;
   }
   .card:hover {
       transform: translateY(-3px);
   }
   ```

2. **导航链接悬停效果**：
   - 背景色变更为浅灰色
   - 文字颜色变更为主题蓝色

### 3.4.5 主题配色方案

| 元素 | 颜色代码 | 使用位置 |
|------|---------|---------|
| 导航栏背景 | bg-dark | 顶部导航栏 |
| 侧边栏背景 | bg-light | 左侧菜单栏 |
| 主题蓝 | #007bff | 链接和强调色 |
| 成功绿 | bg-success | 已使用房间卡片 |
| 警告黄 | bg-warning | 空闲房间卡片 |
| 信息蓝 | bg-primary | 房间总数卡片 |

### 3.4.6 关键技术栈

- **框架**：Bootstrap 5.3.0
- **图标库**：Bootstrap Icons 1.7.2
- **JS库**：
  - jQuery 3.6.0
  - Bootstrap Bundle

### 3. 核心业务流程

#### 3.1 房间预约流程
```mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 待审批: 提交预约
    待审批 --> 已预约: 审批通过
    待审批 --> 空闲: 审批拒绝
    已预约 --> 使用中: 开始使用
    使用中 --> 空闲: 使用结束
    使用中 --> 维护中: 报修
    维护中 --> 空闲: 维护完成
```

#### 3.2 用户交互时序
```mermaid
sequenceDiagram
    actor 用户
    participant 前端 as 前端页面
    participant 后端 as 后端服务
    participant DB as 数据库

    用户->>前端: 1. 访问预约页面
    前端->>后端: 2. 获取可用房间
    后端->>DB: 3. 查询房间状态
    DB-->>后端: 4. 返回房间列表
    后端-->>前端: 5. 返回可用房间
    前端-->>用户: 6. 显示可用房间
    用户->>前端: 7. 提交预约申请
    前端->>后端: 8. 发送预约请求
    后端->>DB: 9. 保存预约信息
    DB-->>后端: 10. 保存成功
    后端-->>前端: 11. 返回预约结果
    前端-->>用户: 12. 显示预约成功
```

### 4. 系统类图设计

#### 4.1 房间管理模块类图
```mermaid
classDiagram
    class Room {
        +int id
        +String roomNumber
        +String status
        +double area
        +String type
        +String description
        +book(User user)
        +maintain()
        +release()
    }

    class Booking {
        +int id
        +DateTime startTime
        +DateTime endTime
        +String status
        +approve()
        +reject()
        +complete()
    }

    class User {
        +int id
        +String username
        +String realName
        +String role
        +makeBooking()
        +cancelBooking()
    }

    Room "1" -- "*" Booking
    Booking "*" -- "1" User
```

### 5. 功能使用统计

#### 5.1 系统功能使用分布
```mermaid
pie
    title 系统功能使用占比
    "房间预约" : 40
    "设备管理" : 25
    "用户管理" : 20
    "统计分析" : 15
```

### 6. 开发进度规划

```mermaid
gantt
    title 系统开发进度
    dateFormat  YYYY-MM-DD
    section 前端开发
    页面设计     :a1, 2024-03-01, 7d
    组件开发     :a2, after a1, 10d
    页面整合     :a3, after a2, 5d
    section 后端开发
    数据库设计   :b1, 2024-03-01, 5d
    API开发     :b2, after b1, 15d
    单元测试    :b3, after b2, 5d
    section 测试阶段
    功能测试    :c1, 2024-04-01, 7d
    性能测试    :c2, after c1, 5d
    验收测试    :c3, after c2, 3d
```

### 7. 系统功能思维导图

```mermaid
mindmap
    root((产教融合大楼<br>管理系统))
        房间管理
            房间信息维护
            预约管理
            使用统计
        设备管理
            设备台账
            维护记录
            报修处理
        用户管理
            用户信息
            权限控制
            操作日志
        统计分析
            使用率分析
            预约统计
            报表导出
```

### 8. 性能指标对比

| 功能模块 | 响应时间 | 并发数 | CPU占用 | 内存占用 | 评分 |
|---------|---------|--------|---------|----------|------|
| 房间预约 | <200ms | 100+ | 30% | 256MB | ⭐⭐⭐⭐⭐ |
| 用户认证 | <100ms | 500+ | 20% | 128MB | ⭐⭐⭐⭐ |
| 统计分析 | <500ms | 50+ | 40% | 512MB | ⭐⭐⭐ |
| 报表导出 | <1s | 10+ | 60% | 1GB | ⭐⭐ |

### 9. 数据流程图

```mermaid
flowchart TD
    A[用户输入] --> B{是否登录?}
    B -->|是| C[显示主页]
    B -->|否| D[跳转登录]
    C --> E{选择功能}
    E -->|房间预约| F[预约流程]
    E -->|查看统计| G[统计分析]
    E -->|用户管理| H[权限检查]
    F --> I{预约确认}
    I -->|确认| J[更新数据库]
    I -->|取消| C
    G --> K[生成报表]
    H --> L{有权限?}
    L -->|是| M[执行操作]
    L -->|否| N[提示无权限]
```