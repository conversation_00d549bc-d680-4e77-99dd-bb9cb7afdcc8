# 产教融合大楼数字化管理系统开发文档

## 项目概述

产教融合大楼数字化管理系统旨在通过数字化手段提升产教融合大楼的管理效率和服务质量，实现大楼运营的智能化、精细化和人性化。通过整合物联网、大数据、人工智能等技术，构建一个现代化的智慧建筑管理平台。

## 开发计划

根据需求分析，我们将分五个阶段实施开发计划：

### 第一阶段：教室布局图基础功能开发

1. **创建教室布局模型**
   - 设计教室布局数据结构
   - 实现教室基本信息管理（编号、名称、状态等）

2. **开发2D楼层教室布局图**
   - 实现1楼和2楼的平面图展示
   - 显示教室编号、名称和排课状态
   - 实现教室状态的可视化展示（空闲、使用中、维护中）

3. **教室详情页面开发**
   - 显示教室内部布局
   - 展示关键设备位置和状态
   - 实现教室基本信息查看和编辑功能

### 第二阶段：设备管理功能开发

1. **设备模型完善**
   - 扩展设备模型，增加设备类型、状态、参数等属性
   - 实现设备与教室的关联关系

2. **设备状态监控**
   - 开发设备状态实时监控功能
   - 实现设备运行参数展示
   - 开发设备故障预警功能

3. **设备远程控制**
   - 实现设备远程开关控制
   - 开发设备参数远程调整功能
   - 实现设备控制界面

### 第三阶段：摄像头集成与人员管理

1. **摄像头接入**
   - 实现1-2个摄像头的接入
   - 开发视频流展示功能
   - 实现摄像头控制功能

2. **人员状态管理**
   - 开发人员进出记录功能
   - 实现教室人员状态查询
   - 开发人员统计分析功能

### 第四阶段：接口协议与数据交换

1. **设备接口模式开发**
   - 实现设备接口模式查看功能
   - 开发接口数据展示功能
   - 实现接口协议匹配功能

2. **口令维护管理**
   - 开发教室口令管理功能
   - 实现口令权限控制
   - 开发口令使用记录功能

### 第五阶段：实践教学管理与评价

1. **任务管理**
   - 开发实践教学任务管理功能
   - 实现任务目标设置和跟踪
   - 开发任务分配和通知功能

2. **操作流程管理**
   - 实现操作流程定义和展示
   - 开发操作步骤跟踪功能
   - 实现操作指导功能

3. **结果验证与评价**
   - 开发结果验证功能
   - 实现评价标准设置
   - 开发评价结果统计和分析功能

## 技术架构

本项目采用以下技术架构：

- **前端**：JSP + Bootstrap 5 + Chart.js
- **后端**：Java Servlet + JDBC
- **数据库**：MySQL
- **服务器**：Tomcat 9

## 数据模型

### 主要实体

1. **Room（教室）**
   - id：教室ID
   - roomNumber：教室编号
   - floorNumber：楼层号
   - roomType：教室类型
   - area：面积
   - status：状态（空闲、使用中、维护中）
   - description：描述

2. **Device（设备）**
   - id：设备ID
   - name：设备名称
   - type：设备类型
   - location：位置
   - status：状态（正常、故障、维护中）
   - lastMaintenanceDate：最后维护日期
   - nextMaintenanceDate：下次维护日期

3. **User（用户）**
   - id：用户ID
   - username：用户名
   - password：密码
   - realName：真实姓名
   - role：角色（管理员、普通用户）

4. **Reservation（预约）**
   - id：预约ID
   - userId：用户ID
   - roomId：教室ID
   - deviceId：设备ID
   - startTime：开始时间
   - endTime：结束时间
   - purpose：使用目的
   - status：状态（待审核、已批准、已拒绝、已完成、已取消）

## 当前进度

目前已完成第一阶段和第二阶段的开发工作，包括：

### 第一阶段：教室布局图基础功能
1. 完善了教室布局模型，添加了与布局相关的属性
2. 创建了RoomLayout模型，用于存储教室内部布局信息
3. 开发了2D楼层教室布局图展示功能
4. 实现了教室详情页面，展示教室内部布局和设备状态
5. 实现了设备控制功能

### 第二阶段：设备管理功能
1. 完善了设备模型，增加了与设备状态和参数相关的属性
2. 创建了设备参数、设备告警和设备维护记录模型
3. 开发了设备状态实时监控功能
4. 实现了设备运行参数展示和历史记录
5. 开发了设备故障预警功能
6. 实现了设备参数图表展示
7. 开发了设备告警管理功能

## 下一步工作

1. 实现摄像头接入功能
2. 开发视频流展示功能
3. 实现摄像头控制功能
4. 开发人员进出记录功能
5. 实现教室人员状态查询功能

## 更新日志

- 2024-04-16：初始化项目文档，制定开发计划
- 2024-04-17：完成第一阶段开发，实现教室布局和设备管理基础功能
- 2024-04-18：完成第二阶段开发，实现设备监控和故障预警功能
- 2024-04-19：修复设备监控页面图表显示问题，优化界面样式和用户体验
- 2024-04-20：修复设备监控面板中内容遮挡问题，优化未解决告警显示，改进页面标题一致性
- 2024-04-21：进一步优化设备监控面板布局，解决未解决告警部分的显示问题，增强视觉层次和用户体验
- 2024-04-22：全面重构设备监控页面布局，将页面分为三个独立区域，添加清晰的视觉分隔，解决内容遮挡问题，优化响应式设计
- 2024-04-23：改进设备监控页面的图表功能，添加设备选择器，实现根据选择的设备显示相应的参数监控图表，支持实时数据更新
- 2024-04-24：扩展设备表结构，添加运行参数相关字段，增加更多测试设备数据，优化设备监控页面的设备选择功能
- 2024-04-25：修复设备表结构更新脚本，添加列存在性检查，避免重复添加列导致的错误
- 2024-04-26：优化设备监控页面的设备参数显示，解决温度、湿度和功耗信息显示遮挡问题，改进数据格式化
- 2024-04-27：重构设备监控页面布局，将页面分为左右两栏结构，左侧显示设备状态概览和在线设备列表，右侧显示参数监控图表和未解决告警，解决参数监控图表遮挡在线设备列表的问题
- 2024-04-28：优化在线设备列表的间距和布局，增加设备卡片之间的间距，改进设备参数显示区域的样式，提升用户体验
- 2024-04-28：优化设备详情页面布局，增加各区域间距，改进实时参数卡片样式，优化图表和告警区域显示，解决内容遮挡问题
- 2024-04-29：重构设备详情页面布局，采用选项卡式设计展示历史数据和告警维护记录，优化设备信息展示方式，彻底解决内容遮挡问题
- 2024-04-30：大幅增强设备详情页面的数据展示，添加技术规格、网络信息、维护信息等详细数据，增加参数历史数据表格和统计信息，提供更全面的设备信息
- 2024-05-01：优化房间管理页面，删除重复的统计卡片，提高页面整洁度和用户体验
- 2024-05-01：全面美化设备管理页面，优化页面布局，美化统计卡片，改进设备列表表格，优化添加设备模态框，添加搜索和筛选功能，增强交互体验
- 2024-05-02：全面美化用户管理页面，优化页面布局，美化统计卡片，添加用户头像显示，改进用户列表表格，优化添加用户模态框，添加搜索和筛选功能，增强交互体验
