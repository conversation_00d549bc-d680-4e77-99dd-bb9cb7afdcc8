# 视频流功能集成文档

## 概述

本文档描述了EduFusionCenter项目中新集成的视频流功能，该功能将TEST目录中的RTSP视频转码服务融合到了主项目的摄像头管理系统中。

## 功能特性

### 核心功能
- **RTSP到HLS转码**: 使用FFmpeg将摄像头的RTSP流转换为Web兼容的HLS格式
- **实时视频播放**: 在Web浏览器中实时播放摄像头视频流
- **流状态管理**: 支持启动、停止、重启视频流
- **自动重连**: 流异常时自动重启，确保连续监控
- **多路并发**: 支持多个摄像头同时进行视频转码

### 技术特点
- 低延迟流媒体传输（2-6秒延迟）
- 跨浏览器兼容的HLS.js播放器
- 自动错误恢复机制
- 流状态实时监控
- RESTful API接口

## 系统架构

### 组件结构
```
EduFusionCenter
├── Java Web应用 (主系统)
│   ├── 摄像头管理模块
│   ├── 视频流服务接口
│   └── Web界面
├── Node.js流服务器 (新增)
│   ├── FFmpeg转码引擎
│   ├── HLS文件生成
│   └── 流状态管理
└── 前端播放器
    ├── HLS.js播放器
    ├── 流控制界面
    └── 状态监控
```

### 数据流
1. 摄像头 → RTSP流 → Node.js服务器
2. Node.js服务器 → FFmpeg转码 → HLS文件
3. Web浏览器 → HLS播放器 → 实时视频显示

## 新增文件和修改

### 新增文件
- `package.json` - Node.js项目配置
- `stream-server.js` - 视频流转码服务器
- `start-stream-server.ps1` - 启动脚本
- `sql/13_update_camera_table_for_streaming.sql` - 数据库更新脚本
- `src/main/java/com/building/service/VideoStreamService.java` - 视频流服务接口
- `src/main/java/com/building/service/impl/VideoStreamServiceImpl.java` - 视频流服务实现

### 修改文件
- `src/main/java/com/building/model/Camera.java` - 添加流相关字段
- `src/main/java/com/building/dao/CameraDao.java` - 添加流相关数据库操作
- `src/main/java/com/building/servlet/VideoStreamServlet.java` - 集成流控制功能
- `src/main/webapp/WEB-INF/views/camera/stream.jsp` - 更新视频播放界面
- `src/main/webapp/WEB-INF/views/camera/stream-content.jsp` - 添加HLS播放器

## 数据库变更

### 摄像头表新增字段
```sql
ALTER TABLE camera 
ADD COLUMN stream_id VARCHAR(100) COMMENT '流ID，用于视频转码服务',
ADD COLUMN stream_format VARCHAR(20) DEFAULT 'hls' COMMENT '流格式：hls, flv',
ADD COLUMN stream_status TINYINT DEFAULT 0 COMMENT '流状态：0-未启动 1-运行中 2-错误',
ADD COLUMN stream_url VARCHAR(255) COMMENT '转码后的流URL',
ADD COLUMN last_stream_time DATETIME COMMENT '最后流活动时间',
ADD COLUMN stream_error_count INT DEFAULT 0 COMMENT '流错误次数',
ADD COLUMN auto_restart TINYINT DEFAULT 1 COMMENT '是否自动重启流：0-否 1-是';
```

### 新增流日志表
```sql
CREATE TABLE stream_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    camera_id INT NOT NULL,
    stream_id VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    message TEXT,
    error_details TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API接口

### 流控制接口
- `POST /camera/stream?action=startStream` - 启动视频流
- `POST /camera/stream?action=stopStream` - 停止视频流
- `POST /camera/stream?action=restartStream` - 重启视频流
- `POST /camera/stream?action=getStreamUrl` - 获取流URL
- `POST /camera/stream?action=getStreamStatus` - 获取流状态

### Node.js服务器接口
- `POST /api/stream/start` - 启动RTSP转码
- `POST /api/stream/stop` - 停止转码
- `GET /api/stream/status` - 获取所有流状态
- `GET /api/health` - 健康检查

## 部署说明

### 环境要求
- Node.js 14.0+
- FFmpeg 4.0+
- Java 8+
- MySQL 5.7+

### 启动步骤
1. 安装Node.js依赖：`npm install`
2. 确保FFmpeg已安装并在PATH中
3. 执行数据库更新脚本
4. 启动Node.js流服务器：`./start-stream-server.ps1`
5. 启动Java Web应用

### 配置说明
- 流服务器默认端口：3001
- HLS片段时长：2秒
- 最大缓冲片段：8个
- 自动重启延迟：2-5秒

## 使用指南

### 基本操作
1. 进入摄像头详情页面
2. 点击"视频流"标签
3. 点击"启动视频流"按钮
4. 等待3-5秒后视频开始播放
5. 使用控制按钮管理流状态

### 故障排除
- 如果视频无法播放，检查FFmpeg是否正确安装
- 如果流频繁断开，检查网络连接和RTSP URL
- 查看浏览器控制台获取详细错误信息
- 检查Node.js服务器日志

## 性能优化

### 建议配置
- 服务器内存：至少4GB
- 网络带宽：每路流至少2Mbps
- 并发流数量：建议不超过10路

### 监控指标
- 流启动成功率
- 平均延迟时间
- 错误重启次数
- 服务器资源使用率

## 未来扩展

### 计划功能
- 录像功能
- 多码率自适应
- 移动端优化
- 云存储集成
- AI视频分析

---
*文档版本: 1.0*  
*最后更新: 2024年12月*
