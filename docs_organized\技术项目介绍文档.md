# 产教融合大楼数字化管理系统 - 技术项目介绍文档

## 📋 文档信息
- **项目名称**: 产教融合大楼数字化管理系统 (EduFusionCenter)
- **文档版本**: v2.0.0
- **创建日期**: 2025-06-24
- **最后更新**: 2025-06-24
- **文档类型**: 技术架构文档
- **目标读者**: 技术人员、架构师、开发工程师

## 🎯 项目概述

### 项目背景
产教融合大楼数字化管理系统是一个基于Java Web技术栈的企业级应用系统，采用MVC架构模式，集成了设备管理、空间管理、视频监控、预约系统等多个业务模块，为产教融合大楼提供全方位的数字化管理解决方案。

### 技术目标
- **高可用性**: 系统可用性达到99.9%以上
- **高性能**: 支持100+并发用户，响应时间<3秒
- **可扩展性**: 模块化设计，支持功能扩展和系统集成
- **安全性**: 多层次安全防护，数据加密传输和存储
- **易维护性**: 标准化代码结构，完善的日志和监控

### 核心技术栈
```mermaid
graph TB
    subgraph "前端技术栈"
        A1[HTML5/CSS3]
        A2[JavaScript ES6+]
        A3[Bootstrap 5.3.0]
        A4[jQuery 3.6.0]
        A5[Chart.js 3.9.1]
        A6[WebSocket API]
    end
    
    subgraph "后端技术栈"
        B1[Java 8+]
        B2[Servlet API 4.0]
        B3[JSP 2.3]
        B4[JSTL 1.2]
        B5[JDBC 4.2]
        B6[Jackson 2.13]
    end
    
    subgraph "数据库技术"
        C1[MySQL 8.0]
        C2[HikariCP连接池]
        C3[事务管理]
    end
    
    subgraph "服务器技术"
        D1[Apache Tomcat 9]
        D2[Node.js 16+]
        D3[FFmpeg 4.4]
        D4[Nginx 1.20]
    end
    
    subgraph "开发工具"
        E1[Maven 3.8]
        E2[Git 2.30]
        E3[IntelliJ IDEA]
        E4[VSCode]
    end
```

## 🏗️ 系统架构设计

### 整体架构
```mermaid
graph TB
    subgraph "客户端层 (Client Layer)"
        A1[Web浏览器]
        A2[移动端浏览器]
        A3[管理终端]
    end
    
    subgraph "表示层 (Presentation Layer)"
        B1[JSP页面]
        B2[静态资源]
        B3[JavaScript组件]
        B4[CSS样式]
    end
    
    subgraph "控制层 (Controller Layer)"
        C1[用户管理Servlet]
        C2[房间管理Servlet]
        C3[设备管理Servlet]
        C4[摄像头控制Servlet]
        C5[预约管理Servlet]
        C6[系统配置Servlet]
    end
    
    subgraph "业务逻辑层 (Service Layer)"
        D1[用户服务]
        D2[房间服务]
        D3[设备服务]
        D4[视频流服务]
        D5[预约服务]
        D6[统计分析服务]
    end
    
    subgraph "数据访问层 (DAO Layer)"
        E1[用户DAO]
        E2[房间DAO]
        E3[设备DAO]
        E4[摄像头DAO]
        E5[预约DAO]
        E6[系统配置DAO]
    end
    
    subgraph "数据持久层 (Persistence Layer)"
        F1[(MySQL主数据库)]
        F2[(MySQL从数据库)]
        F3[文件存储系统]
        F4[日志文件系统]
    end
    
    subgraph "外部系统层 (External Systems)"
        G1[RTSP摄像头设备]
        G2[IoT传感器设备]
        G3[流媒体服务器]
        G4[第三方API]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    B1 --> C5
    B1 --> C6
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
    C5 --> D5
    C6 --> D6
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
    D5 --> E5
    D6 --> E6
    
    E1 --> F1
    E2 --> F1
    E3 --> F1
    E4 --> F1
    E5 --> F1
    E6 --> F1
    
    F1 --> F2
    
    D4 --> G1
    D3 --> G2
    D4 --> G3
    D6 --> G4
```

### 技术架构分层说明

#### 1. 客户端层 (Client Layer)
- **Web浏览器**: 支持Chrome、Firefox、Safari、Edge等主流浏览器
- **移动端浏览器**: 响应式设计，支持iOS Safari、Android Chrome
- **管理终端**: 专用管理界面，支持高级管理功能

#### 2. 表示层 (Presentation Layer)
- **JSP页面**: 动态页面生成，支持JSTL和EL表达式
- **静态资源**: CSS、JavaScript、图片等静态文件
- **JavaScript组件**: 前端交互逻辑和AJAX通信
- **CSS样式**: Bootstrap框架 + 自定义样式

#### 3. 控制层 (Controller Layer)
- **Servlet控制器**: 处理HTTP请求，控制业务流程
- **请求路由**: 基于URL模式的请求分发
- **参数验证**: 输入参数的格式和安全验证
- **异常处理**: 统一的异常处理和错误响应

#### 4. 业务逻辑层 (Service Layer)
- **业务服务**: 封装核心业务逻辑
- **事务管理**: 数据库事务的统一管理
- **业务规则**: 复杂业务规则的实现
- **外部集成**: 与外部系统的集成逻辑

#### 5. 数据访问层 (DAO Layer)
- **数据访问对象**: 封装数据库操作
- **SQL管理**: 统一的SQL语句管理
- **连接池管理**: 数据库连接的高效管理
- **缓存机制**: 数据缓存和性能优化

#### 6. 数据持久层 (Persistence Layer)
- **MySQL数据库**: 主要的关系型数据存储
- **文件存储**: 图片、视频等文件的存储
- **日志系统**: 系统运行日志和审计日志
- **备份机制**: 数据备份和恢复策略

## 🎨 用例图设计

### 系统用例图
```mermaid
graph TB
    subgraph "系统边界"
        subgraph "用户管理"
            UC1[用户登录]
            UC2[用户注册]
            UC3[密码修改]
            UC4[权限管理]
        end
        
        subgraph "房间管理"
            UC5[查看楼层布局]
            UC6[房间信息管理]
            UC7[房间状态监控]
            UC8[房间预约]
        end
        
        subgraph "设备管理"
            UC9[设备信息管理]
            UC10[设备状态监控]
            UC11[设备远程控制]
            UC12[设备维护管理]
        end
        
        subgraph "监控管理"
            UC13[摄像头管理]
            UC14[视频流查看]
            UC15[人员统计]
            UC16[告警处理]
        end
        
        subgraph "预约管理"
            UC17[预约申请]
            UC18[预约审批]
            UC19[预约查询]
            UC20[使用记录]
        end
        
        subgraph "统计分析"
            UC21[使用率统计]
            UC22[报表生成]
            UC23[数据导出]
            UC24[趋势分析]
        end
    end
    
    subgraph "参与者"
        Actor1[管理员]
        Actor2[普通用户]
        Actor3[维护人员]
        Actor4[系统管理员]
    end
    
    Actor1 --> UC1
    Actor1 --> UC4
    Actor1 --> UC6
    Actor1 --> UC7
    Actor1 --> UC9
    Actor1 --> UC10
    Actor1 --> UC11
    Actor1 --> UC13
    Actor1 --> UC14
    Actor1 --> UC18
    Actor1 --> UC21
    Actor1 --> UC22
    
    Actor2 --> UC1
    Actor2 --> UC3
    Actor2 --> UC5
    Actor2 --> UC8
    Actor2 --> UC17
    Actor2 --> UC19
    
    Actor3 --> UC1
    Actor3 --> UC10
    Actor3 --> UC12
    Actor3 --> UC16
    Actor3 --> UC20
    
    Actor4 --> UC1
    Actor4 --> UC2
    Actor4 --> UC4
    Actor4 --> UC23
    Actor4 --> UC24
```

### 用例详细说明

#### 用户管理用例
| 用例ID | 用例名称 | 参与者 | 前置条件 | 后置条件 |
|--------|---------|--------|---------|---------|
| UC1 | 用户登录 | 所有用户 | 用户已注册 | 用户成功登录系统 |
| UC2 | 用户注册 | 系统管理员 | 管理员权限 | 新用户账户创建成功 |
| UC3 | 密码修改 | 所有用户 | 用户已登录 | 密码修改成功 |
| UC4 | 权限管理 | 管理员 | 管理员权限 | 用户权限设置完成 |

#### 设备管理用例
| 用例ID | 用例名称 | 参与者 | 前置条件 | 后置条件 |
|--------|---------|--------|---------|---------|
| UC9 | 设备信息管理 | 管理员 | 管理员权限 | 设备信息更新完成 |
| UC10 | 设备状态监控 | 管理员、维护人员 | 设备已连接 | 获取设备实时状态 |
| UC11 | 设备远程控制 | 管理员 | 设备支持远程控制 | 设备控制命令执行成功 |
| UC12 | 设备维护管理 | 维护人员 | 维护人员权限 | 维护记录更新完成 |

## 📊 类图设计

### 核心业务类图
```mermaid
classDiagram
    class User {
        -int id
        -String username
        -String password
        -String realName
        -String role
        -Timestamp createTime
        -Timestamp lastLoginTime
        +login(String username, String password) boolean
        +logout() void
        +changePassword(String oldPwd, String newPwd) boolean
        +hasPermission(String permission) boolean
    }
    
    class Room {
        -int id
        -String roomNumber
        -int floorNumber
        -String roomType
        -double area
        -String status
        -String description
        -int capacity
        -String shape
        -int width
        -int length
        -String position
        +book(User user, DateTime startTime, DateTime endTime) boolean
        +release() void
        +maintain() void
        +getAvailableTime() List~TimeSlot~
    }
    
    class Device {
        -int id
        -String name
        -String type
        -String location
        -String status
        -String manufacturer
        -String model
        -String serialNumber
        -String ipAddress
        -double temperature
        -double humidity
        -double powerConsumption
        -int connectionStatus
        +turnOn() boolean
        +turnOff() boolean
        +restart() boolean
        +getParameters() Map~String,Object~
        +updateStatus(String status) void
    }
    
    class Camera {
        -int id
        -String name
        -String ipAddress
        -int port
        -String username
        -String password
        -String rtspUrl
        -String location
        -String brand
        -String model
        -int status
        -int roomId
        +startStream() boolean
        +stopStream() boolean
        +captureImage() String
        +moveCamera(String direction) boolean
        +zoom(int level) boolean
    }
    
    class Reservation {
        -int id
        -int roomId
        -int deviceId
        -int userId
        -DateTime startTime
        -DateTime endTime
        -String purpose
        -String status
        -Timestamp createTime
        +approve() void
        +reject(String reason) void
        +cancel() void
        +complete() void
        +isConflict(Reservation other) boolean
    }
    
    class PersonRecord {
        -int id
        -int cameraId
        -int roomId
        -int personCount
        -Timestamp recordTime
        -String imageUrl
        +save() void
        +getStatistics(Date date) Map~String,Integer~
    }
    
    User ||--o{ Reservation : creates
    Room ||--o{ Reservation : reserved
    Device ||--o{ Reservation : reserved
    Room ||--o{ Camera : contains
    Camera ||--o{ PersonRecord : records
    Room ||--o{ PersonRecord : in
    Room ||--o{ Device : contains
```

### DAO层类图
```mermaid
classDiagram
    class BaseDao {
        #Connection connection
        #PreparedStatement pstmt
        #ResultSet rs
        +getConnection() Connection
        +closeResources() void
        +executeQuery(String sql, Object... params) ResultSet
        +executeUpdate(String sql, Object... params) int
    }
    
    class UserDao {
        +getAllUsers() List~User~
        +getUserById(int id) User
        +getUserByUsername(String username) User
        +addUser(User user) boolean
        +updateUser(User user) boolean
        +deleteUser(int id) boolean
        +validateUser(String username, String password) User
    }
    
    class RoomDao {
        +getAllRooms() List~Room~
        +getRoomById(int id) Room
        +getRoomsByFloor(int floor) List~Room~
        +addRoom(Room room) boolean
        +updateRoom(Room room) boolean
        +deleteRoom(int id) boolean
        +getRoomStats() Map~String,Integer~
    }
    
    class DeviceDao {
        +getAllDevices() List~Device~
        +getDeviceById(int id) Device
        +getDevicesByRoom(int roomId) List~Device~
        +addDevice(Device device) boolean
        +updateDevice(Device device) boolean
        +deleteDevice(int id) boolean
        +updateDeviceStatus(int id, String status) boolean
        +getDeviceStats() Map~String,Integer~
    }
    
    class CameraDao {
        +getAllCameras() List~Camera~
        +getCameraById(int id) Camera
        +getCamerasByRoom(int roomId) List~Camera~
        +addCamera(Camera camera) boolean
        +updateCamera(Camera camera) boolean
        +deleteCamera(int id) boolean
        +updateCameraStreamStatus(int id, int status, String streamUrl) boolean
    }
    
    class ReservationDao {
        +getAllReservations() List~Reservation~
        +getReservationById(int id) Reservation
        +getReservationsByUser(int userId) List~Reservation~
        +addReservation(Reservation reservation) boolean
        +updateReservation(Reservation reservation) boolean
        +deleteReservation(int id) boolean
        +checkConflict(Reservation reservation) boolean
    }
    
    BaseDao <|-- UserDao
    BaseDao <|-- RoomDao
    BaseDao <|-- DeviceDao
    BaseDao <|-- CameraDao
    BaseDao <|-- ReservationDao
```

## 🔄 序列图设计

### 用户登录序列图
```mermaid
sequenceDiagram
    participant U as 用户
    participant B as 浏览器
    participant LS as LoginServlet
    participant US as UserService
    participant UD as UserDao
    participant DB as 数据库
    
    U->>B: 1. 输入用户名密码
    B->>LS: 2. POST /login
    LS->>LS: 3. 参数验证
    LS->>US: 4. validateUser(username, password)
    US->>UD: 5. getUserByUsername(username)
    UD->>DB: 6. SELECT * FROM user WHERE username=?
    DB-->>UD: 7. 返回用户信息
    UD-->>US: 8. 返回User对象
    US->>US: 9. 验证密码
    US-->>LS: 10. 返回验证结果
    LS->>LS: 11. 创建Session
    LS->>UD: 12. updateLastLoginTime(userId)
    UD->>DB: 13. UPDATE user SET last_login_time=?
    LS-->>B: 14. 重定向到主页
    B-->>U: 15. 显示主页面
```

### 设备控制序列图
```mermaid
sequenceDiagram
    participant U as 用户
    participant B as 浏览器
    participant DCS as DeviceControlServlet
    participant DS as DeviceService
    participant DD as DeviceDao
    participant DB as 数据库
    participant IOT as IoT设备
    
    U->>B: 1. 点击设备控制按钮
    B->>DCS: 2. POST /device/control
    DCS->>DCS: 3. 权限验证
    DCS->>DS: 4. controlDevice(deviceId, action)
    DS->>DD: 5. getDeviceById(deviceId)
    DD->>DB: 6. SELECT * FROM device WHERE id=?
    DB-->>DD: 7. 返回设备信息
    DD-->>DS: 8. 返回Device对象
    DS->>IOT: 9. 发送控制命令
    IOT-->>DS: 10. 返回执行结果
    DS->>DD: 11. updateDeviceStatus(deviceId, newStatus)
    DD->>DB: 12. UPDATE device SET status=?
    DS-->>DCS: 13. 返回控制结果
    DCS-->>B: 14. JSON响应
    B-->>U: 15. 更新界面状态
```

### 预约申请序列图
```mermaid
sequenceDiagram
    participant U as 用户
    participant B as 浏览器
    participant RS as ReservationServlet
    participant ReS as ReservationService
    participant RD as ReservationDao
    participant RoD as RoomDao
    participant DB as 数据库
    
    U->>B: 1. 填写预约信息
    B->>RS: 2. POST /reservation/add
    RS->>RS: 3. 参数验证
    RS->>ReS: 4. createReservation(reservation)
    ReS->>RD: 5. checkConflict(reservation)
    RD->>DB: 6. 查询时间冲突
    DB-->>RD: 7. 返回冲突检查结果
    RD-->>ReS: 8. 返回冲突状态
    alt 无冲突
        ReS->>RD: 9. addReservation(reservation)
        RD->>DB: 10. INSERT INTO reservation
        ReS->>RoD: 11. updateRoomStatus(roomId, "已预约")
        RoD->>DB: 12. UPDATE room SET status=?
        ReS-->>RS: 13. 返回成功结果
        RS-->>B: 14. 成功响应
        B-->>U: 15. 显示预约成功
    else 有冲突
        ReS-->>RS: 16. 返回冲突错误
        RS-->>B: 17. 错误响应
        B-->>U: 18. 显示冲突提示
    end
```

## 🔄 活动图设计

### 设备监控活动图
```mermaid
flowchart TD
    A[开始设备监控] --> B{设备是否在线?}
    B -->|是| C[读取设备参数]
    B -->|否| D[记录离线状态]

    C --> E{参数是否正常?}
    E -->|是| F[更新正常状态]
    E -->|否| G[生成告警]

    G --> H[发送告警通知]
    H --> I[记录告警日志]
    I --> J[等待处理]

    F --> K[存储监控数据]
    K --> L{是否继续监控?}
    L -->|是| M[等待下次采集]
    L -->|否| N[停止监控]

    M --> C
    D --> O[尝试重新连接]
    O --> P{重连成功?}
    P -->|是| C
    P -->|否| Q[增加重连间隔]
    Q --> O

    J --> R{告警是否处理?}
    R -->|是| S[更新告警状态]
    R -->|否| J
    S --> K

    N --> T[结束]
```

### 预约审批活动图
```mermaid
flowchart TD
    A[收到预约申请] --> B[验证申请信息]
    B --> C{信息是否完整?}
    C -->|否| D[返回错误信息]
    C -->|是| E[检查时间冲突]

    E --> F{是否有冲突?}
    F -->|是| G[返回冲突提示]
    F -->|否| H[检查资源可用性]

    H --> I{资源是否可用?}
    I -->|否| J[返回资源不可用]
    I -->|是| K[提交审批流程]

    K --> L{需要人工审批?}
    L -->|否| M[自动批准]
    L -->|是| N[等待管理员审批]

    N --> O{管理员决定}
    O -->|批准| P[更新预约状态为已批准]
    O -->|拒绝| Q[更新预约状态为已拒绝]

    M --> P
    P --> R[发送批准通知]
    Q --> S[发送拒绝通知]

    R --> T[更新资源状态]
    S --> U[记录拒绝原因]
    T --> V[结束]
    U --> V

    D --> V
    G --> V
    J --> V
```

### 视频流处理活动图
```mermaid
flowchart TD
    A[启动视频流请求] --> B[验证摄像头信息]
    B --> C{摄像头是否在线?}
    C -->|否| D[返回设备离线错误]
    C -->|是| E[检查RTSP连接]

    E --> F{RTSP连接成功?}
    F -->|否| G[返回连接失败错误]
    F -->|是| H[启动FFmpeg进程]

    H --> I{FFmpeg启动成功?}
    I -->|否| J[返回转码失败错误]
    I -->|是| K[开始视频转码]

    K --> L[生成HLS流]
    L --> M[更新流状态]
    M --> N[返回流URL]

    N --> O[客户端开始播放]
    O --> P{播放是否正常?}
    P -->|否| Q[检查网络状态]
    P -->|是| R[持续监控流状态]

    Q --> S{网络是否正常?}
    S -->|否| T[等待网络恢复]
    S -->|是| U[重启流服务]

    T --> S
    U --> K

    R --> V{用户是否停止?}
    V -->|否| R
    V -->|是| W[停止FFmpeg进程]

    W --> X[清理临时文件]
    X --> Y[更新流状态为停止]
    Y --> Z[结束]

    D --> Z
    G --> Z
    J --> Z
```

## 📊 业务流程分析

### 用户操作流程图
```mermaid
flowchart TD
    A[用户访问系统] --> B{是否已登录?}
    B -->|否| C[跳转登录页面]
    B -->|是| D[显示主页面]

    C --> E[输入用户名密码]
    E --> F[提交登录请求]
    F --> G{验证是否成功?}
    G -->|否| H[显示错误信息]
    G -->|是| D

    H --> E

    D --> I[选择功能模块]
    I --> J{选择哪个模块?}

    J -->|房间管理| K[查看楼层布局]
    J -->|设备管理| L[查看设备列表]
    J -->|监控系统| M[查看监控画面]
    J -->|预约管理| N[查看预约列表]
    J -->|统计分析| O[查看统计报表]

    K --> K1[点击房间]
    K1 --> K2[查看房间详情]
    K2 --> K3[预约房间]

    L --> L1[选择设备]
    L1 --> L2[查看设备详情]
    L2 --> L3[控制设备]

    M --> M1[选择摄像头]
    M1 --> M2[查看视频流]
    M2 --> M3[控制摄像头]

    N --> N1[创建预约]
    N1 --> N2[填写预约信息]
    N2 --> N3[提交申请]

    O --> O1[选择统计类型]
    O1 --> O2[设置查询条件]
    O2 --> O3[生成报表]

    K3 --> P[操作完成]
    L3 --> P
    M3 --> P
    N3 --> P
    O3 --> P

    P --> Q{继续使用?}
    Q -->|是| I
    Q -->|否| R[退出系统]
```

### 系统处理流程图
```mermaid
flowchart TD
    A[接收HTTP请求] --> B[解析请求参数]
    B --> C[验证用户会话]
    C --> D{会话是否有效?}
    D -->|否| E[返回未授权错误]
    D -->|是| F[检查用户权限]

    F --> G{权限是否足够?}
    G -->|否| H[返回权限不足错误]
    G -->|是| I[调用业务逻辑]

    I --> J[执行数据库操作]
    J --> K{操作是否成功?}
    K -->|否| L[回滚事务]
    K -->|是| M[提交事务]

    L --> N[记录错误日志]
    M --> O[记录操作日志]

    N --> P[返回错误响应]
    O --> Q[返回成功响应]

    E --> R[记录安全日志]
    H --> R

    R --> S[结束处理]
    P --> S
    Q --> S
```

### 数据流转流程图
```mermaid
flowchart LR
    subgraph "数据输入"
        A1[用户输入]
        A2[设备数据]
        A3[摄像头数据]
        A4[系统配置]
    end

    subgraph "数据处理"
        B1[参数验证]
        B2[业务逻辑处理]
        B3[数据转换]
        B4[数据聚合]
    end

    subgraph "数据存储"
        C1[(用户数据表)]
        C2[(设备数据表)]
        C3[(监控数据表)]
        C4[(日志数据表)]
    end

    subgraph "数据输出"
        D1[Web页面]
        D2[JSON API]
        D3[报表文件]
        D4[告警通知]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4

    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4

    B1 --> B2
    B2 --> B3
    B3 --> B4
```

### 异常处理流程图
```mermaid
flowchart TD
    A[系统运行] --> B{是否发生异常?}
    B -->|否| A
    B -->|是| C[捕获异常]

    C --> D{异常类型判断}
    D -->|数据库异常| E[数据库连接处理]
    D -->|网络异常| F[网络重连处理]
    D -->|业务异常| G[业务逻辑处理]
    D -->|系统异常| H[系统错误处理]

    E --> E1[检查连接池]
    E1 --> E2[重新建立连接]
    E2 --> E3{连接是否成功?}
    E3 -->|是| I[记录恢复日志]
    E3 -->|否| J[记录失败日志]

    F --> F1[检查网络状态]
    F1 --> F2[尝试重新连接]
    F2 --> F3{重连是否成功?}
    F3 -->|是| I
    F3 -->|否| K[启用备用方案]

    G --> G1[验证业务规则]
    G1 --> G2[返回友好错误信息]
    G2 --> L[用户界面提示]

    H --> H1[保存系统状态]
    H1 --> H2[发送告警通知]
    H2 --> H3[启动故障转移]

    I --> M[继续正常运行]
    J --> N[启动降级服务]
    K --> N
    L --> O[等待用户操作]
    H3 --> P[系统维护模式]

    M --> A
    N --> Q{服务是否恢复?}
    Q -->|是| M
    Q -->|否| N

    O --> R{用户是否重试?}
    R -->|是| A
    R -->|否| S[结束操作]

    P --> T[等待管理员处理]
```

## 🗄️ 数据库设计

### 数据库ER图
```mermaid
erDiagram
    USER {
        int id PK
        varchar username UK
        varchar password
        varchar real_name
        varchar role
        timestamp create_time
        timestamp last_login_time
    }

    ROOM {
        int id PK
        varchar room_number UK
        int floor_number
        varchar room_type
        decimal area
        varchar status
        text description
        int capacity
        varchar shape
        int width
        int length
        varchar position
        timestamp create_time
        timestamp update_time
    }

    DEVICE {
        int id PK
        varchar name
        varchar type
        varchar location
        varchar status
        varchar manufacturer
        varchar model
        varchar serial_number
        varchar ip_address
        varchar mac_address
        int power_status
        double temperature
        double humidity
        double power_consumption
        int runtime
        varchar firmware_version
        timestamp last_online_time
        int connection_status
        timestamp last_maintenance_date
        timestamp next_maintenance_date
    }

    CAMERA {
        int id PK
        varchar name
        varchar ip_address
        int port
        varchar username
        varchar password
        varchar rtsp_url
        varchar location
        varchar brand
        varchar model
        int status
        int room_id FK
        varchar stream_id
        varchar stream_url
        varchar stream_format
        timestamp last_online_time
        timestamp create_time
        timestamp update_time
    }

    RESERVATION {
        int id PK
        int room_id FK
        int device_id FK
        int user_id FK
        datetime start_time
        datetime end_time
        varchar purpose
        varchar status
        timestamp create_time
        timestamp update_time
    }

    PERSON_RECORD {
        int id PK
        int camera_id FK
        int room_id FK
        int person_count
        timestamp record_time
        varchar image_url
    }

    DEVICE_PARAMETER {
        int id PK
        int device_id FK
        varchar parameter_name
        varchar parameter_value
        varchar unit
        timestamp record_time
    }

    DEVICE_ALERT {
        int id PK
        int device_id FK
        varchar alert_type
        varchar alert_level
        varchar alert_message
        varchar status
        timestamp create_time
        timestamp resolve_time
    }

    SYSTEM_LOG {
        int id PK
        varchar log_level
        varchar log_type
        varchar message
        varchar user_id
        varchar ip_address
        timestamp create_time
    }

    USER ||--o{ RESERVATION : "creates"
    ROOM ||--o{ RESERVATION : "reserved_for"
    DEVICE ||--o{ RESERVATION : "reserved_device"
    ROOM ||--o{ CAMERA : "contains"
    CAMERA ||--o{ PERSON_RECORD : "records"
    ROOM ||--o{ PERSON_RECORD : "location"
    DEVICE ||--o{ DEVICE_PARAMETER : "has_parameters"
    DEVICE ||--o{ DEVICE_ALERT : "generates_alerts"
```

### 数据库表结构详细说明

#### 核心业务表
| 表名 | 说明 | 主要字段 | 索引 |
|------|------|---------|------|
| user | 用户信息表 | id, username, password, role | username(UK), role |
| room | 房间信息表 | id, room_number, floor_number, status | room_number(UK), floor_number |
| device | 设备信息表 | id, name, type, status, ip_address | name, type, status, ip_address |
| camera | 摄像头信息表 | id, name, ip_address, room_id, status | ip_address, room_id, status |
| reservation | 预约记录表 | id, room_id, user_id, start_time, status | room_id, user_id, start_time, status |

#### 监控数据表
| 表名 | 说明 | 主要字段 | 索引 |
|------|------|---------|------|
| person_record | 人员记录表 | id, camera_id, room_id, person_count | camera_id, room_id, record_time |
| device_parameter | 设备参数表 | id, device_id, parameter_name, parameter_value | device_id, record_time |
| device_alert | 设备告警表 | id, device_id, alert_type, status | device_id, status, create_time |
| system_log | 系统日志表 | id, log_level, log_type, message | log_level, log_type, create_time |

### 数据库性能优化

#### 索引策略
```sql
-- 用户表索引
CREATE INDEX idx_user_username ON user(username);
CREATE INDEX idx_user_role ON user(role);

-- 房间表索引
CREATE INDEX idx_room_floor ON room(floor_number);
CREATE INDEX idx_room_type ON room(room_type);
CREATE INDEX idx_room_status ON room(status);

-- 设备表索引
CREATE INDEX idx_device_type ON device(type);
CREATE INDEX idx_device_status ON device(status);
CREATE INDEX idx_device_ip ON device(ip_address);

-- 预约表索引
CREATE INDEX idx_reservation_room ON reservation(room_id);
CREATE INDEX idx_reservation_user ON reservation(user_id);
CREATE INDEX idx_reservation_time ON reservation(start_time, end_time);
CREATE INDEX idx_reservation_status ON reservation(status);

-- 监控数据表索引
CREATE INDEX idx_person_record_time ON person_record(record_time);
CREATE INDEX idx_device_parameter_time ON device_parameter(record_time);
CREATE INDEX idx_device_alert_status ON device_alert(status);
CREATE INDEX idx_system_log_time ON system_log(create_time);
```

#### 分区策略
```sql
-- 按时间分区的监控数据表
CREATE TABLE person_record (
    -- 字段定义...
) PARTITION BY RANGE (YEAR(record_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按日期分区的系统日志表
CREATE TABLE system_log (
    -- 字段定义...
) PARTITION BY RANGE (TO_DAYS(create_time)) (
    PARTITION p_current VALUES LESS THAN (TO_DAYS('2025-07-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 🏗️ 系统架构图详解

### 网络拓扑图
```mermaid
graph TB
    subgraph "外网"
        Internet[互联网]
        CDN[CDN服务]
    end

    subgraph "DMZ区"
        LB[负载均衡器]
        Firewall[防火墙]
        Proxy[反向代理]
    end

    subgraph "应用服务器集群"
        Web1[Web服务器1<br/>Tomcat 9]
        Web2[Web服务器2<br/>Tomcat 9]
        Web3[Web服务器3<br/>Tomcat 9]
    end

    subgraph "数据库集群"
        DB_Master[(MySQL主库)]
        DB_Slave1[(MySQL从库1)]
        DB_Slave2[(MySQL从库2)]
        Redis[(Redis缓存)]
    end

    subgraph "文件存储"
        NFS[NFS文件服务器]
        Backup[备份服务器]
    end

    subgraph "监控服务"
        Monitor[监控服务器]
        Log[日志服务器]
    end

    subgraph "外部设备"
        Camera1[摄像头1]
        Camera2[摄像头2]
        IoT1[IoT设备1]
        IoT2[IoT设备2]
        StreamServer[流媒体服务器]
    end

    Internet --> CDN
    CDN --> Firewall
    Firewall --> LB
    LB --> Proxy

    Proxy --> Web1
    Proxy --> Web2
    Proxy --> Web3

    Web1 --> DB_Master
    Web2 --> DB_Master
    Web3 --> DB_Master

    DB_Master --> DB_Slave1
    DB_Master --> DB_Slave2

    Web1 --> Redis
    Web2 --> Redis
    Web3 --> Redis

    Web1 --> NFS
    Web2 --> NFS
    Web3 --> NFS

    NFS --> Backup

    Web1 --> Monitor
    Web2 --> Monitor
    Web3 --> Monitor

    Monitor --> Log

    Web1 --> Camera1
    Web1 --> Camera2
    Web2 --> IoT1
    Web2 --> IoT2
    Web3 --> StreamServer
```

### 模块关系图
```mermaid
graph TB
    subgraph "前端模块"
        UI1[用户界面模块]
        UI2[楼层布局模块]
        UI3[设备控制模块]
        UI4[监控显示模块]
        UI5[统计图表模块]
    end

    subgraph "控制器模块"
        C1[用户控制器]
        C2[房间控制器]
        C3[设备控制器]
        C4[摄像头控制器]
        C5[预约控制器]
        C6[统计控制器]
    end

    subgraph "服务层模块"
        S1[用户服务]
        S2[房间服务]
        S3[设备服务]
        S4[视频流服务]
        S5[预约服务]
        S6[统计服务]
        S7[通知服务]
        S8[安全服务]
    end

    subgraph "数据访问模块"
        D1[用户DAO]
        D2[房间DAO]
        D3[设备DAO]
        D4[摄像头DAO]
        D5[预约DAO]
        D6[日志DAO]
    end

    subgraph "工具模块"
        U1[数据库工具]
        U2[文件工具]
        U3[网络工具]
        U4[加密工具]
        U5[日志工具]
        U6[配置工具]
    end

    subgraph "外部接口模块"
        E1[设备接口]
        E2[摄像头接口]
        E3[流媒体接口]
        E4[第三方API接口]
    end

    UI1 --> C1
    UI2 --> C2
    UI3 --> C3
    UI4 --> C4
    UI5 --> C6

    C1 --> S1
    C2 --> S2
    C3 --> S3
    C4 --> S4
    C5 --> S5
    C6 --> S6

    S1 --> D1
    S2 --> D2
    S3 --> D3
    S4 --> D4
    S5 --> D5
    S6 --> D6

    S1 --> S8
    S2 --> S7
    S3 --> S7
    S4 --> S7
    S5 --> S7

    D1 --> U1
    D2 --> U1
    D3 --> U1
    D4 --> U1
    D5 --> U1
    D6 --> U1

    S3 --> E1
    S4 --> E2
    S4 --> E3
    S6 --> E4

    S8 --> U4
    S7 --> U5
    S1 --> U6
```

### 部署架构图
```mermaid
graph TB
    subgraph "生产环境"
        subgraph "Web层"
            Nginx[Nginx反向代理]
            Tomcat1[Tomcat实例1]
            Tomcat2[Tomcat实例2]
        end

        subgraph "应用层"
            App1[应用服务器1<br/>192.168.1.10]
            App2[应用服务器2<br/>192.168.1.11]
        end

        subgraph "数据层"
            MySQL_M[MySQL主库<br/>************]
            MySQL_S[MySQL从库<br/>************]
            Redis_C[Redis集群<br/>************-32]
        end

        subgraph "存储层"
            NFS_Server[NFS存储<br/>************]
            Backup_Server[备份服务器<br/>************]
        end
    end

    subgraph "测试环境"
        Test_Web[测试Web服务器<br/>************]
        Test_DB[测试数据库<br/>************]
    end

    subgraph "开发环境"
        Dev_Local[开发本地环境<br/>localhost]
        Dev_DB[开发数据库<br/>************]
    end

    subgraph "监控环境"
        Prometheus[Prometheus<br/>************]
        Grafana[Grafana<br/>************]
        ELK[ELK Stack<br/>************-54]
    end

    Nginx --> Tomcat1
    Nginx --> Tomcat2
    Tomcat1 --> App1
    Tomcat2 --> App2

    App1 --> MySQL_M
    App2 --> MySQL_M
    MySQL_M --> MySQL_S

    App1 --> Redis_C
    App2 --> Redis_C

    App1 --> NFS_Server
    App2 --> NFS_Server
    NFS_Server --> Backup_Server

    App1 --> Prometheus
    App2 --> Prometheus
    Prometheus --> Grafana

    App1 --> ELK
    App2 --> ELK
```

## 🔧 功能模块详解

### 用户管理模块
```mermaid
graph TB
    subgraph "用户管理功能"
        A1[用户注册]
        A2[用户登录]
        A3[密码管理]
        A4[权限管理]
        A5[用户信息维护]
        A6[用户状态管理]
    end

    subgraph "技术实现"
        B1[Session管理]
        B2[密码加密]
        B3[权限验证]
        B4[RBAC模型]
        B5[用户缓存]
        B6[审计日志]
    end

    subgraph "数据结构"
        C1[User实体]
        C2[Role实体]
        C3[Permission实体]
        C4[UserSession实体]
    end

    A1 --> B2
    A2 --> B1
    A3 --> B2
    A4 --> B4
    A5 --> B5
    A6 --> B6

    B1 --> C4
    B2 --> C1
    B4 --> C2
    B4 --> C3
```

#### 用户管理技术特性
| 特性 | 技术实现 | 安全级别 | 备注 |
|------|---------|---------|------|
| 密码加密 | BCrypt算法 | 高 | 加盐哈希，防彩虹表攻击 |
| 会话管理 | HttpSession + Redis | 中 | 分布式会话，支持集群 |
| 权限控制 | RBAC模型 | 高 | 基于角色的访问控制 |
| 登录保护 | 验证码 + 限流 | 高 | 防暴力破解 |
| 审计日志 | 数据库记录 | 中 | 完整的操作记录 |

### 设备管理模块
```mermaid
graph TB
    subgraph "设备管理功能"
        D1[设备注册]
        D2[设备监控]
        D3[设备控制]
        D4[参数管理]
        D5[告警管理]
        D6[维护管理]
    end

    subgraph "通信协议"
        E1[HTTP/HTTPS]
        E2[MQTT]
        E3[Modbus TCP]
        E4[SNMP]
        E5[WebSocket]
    end

    subgraph "数据处理"
        F1[实时数据采集]
        F2[数据验证]
        F3[数据存储]
        F4[数据分析]
        F5[告警判断]
        F6[报表生成]
    end

    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
    D5 --> E5

    E1 --> F1
    E2 --> F1
    E3 --> F2
    E4 --> F3
    E5 --> F4

    F1 --> F5
    F2 --> F5
    F3 --> F6
    F4 --> F6
```

#### 设备管理技术规格
| 功能模块 | 技术方案 | 性能指标 | 扩展性 |
|---------|---------|---------|--------|
| 设备发现 | 网络扫描 + 协议探测 | 1000设备/分钟 | 支持插件扩展 |
| 数据采集 | 多线程 + 连接池 | 10000点/秒 | 水平扩展 |
| 实时监控 | WebSocket推送 | <100ms延迟 | 支持集群 |
| 历史数据 | 时序数据库 | 1年数据 | 分区存储 |
| 告警处理 | 规则引擎 | <1秒响应 | 规则可配置 |

### 视频监控模块
```mermaid
graph TB
    subgraph "视频监控功能"
        V1[摄像头管理]
        V2[视频流处理]
        V3[录像管理]
        V4[人员识别]
        V5[告警联动]
        V6[存储管理]
    end

    subgraph "流媒体技术"
        S1[RTSP协议]
        S2[FFmpeg转码]
        S3[HLS流媒体]
        S4[WebRTC]
        S5[流媒体服务器]
    end

    subgraph "AI算法"
        A1[人脸检测]
        A2[人数统计]
        A3[行为分析]
        A4[异常检测]
    end

    V1 --> S1
    V2 --> S2
    V3 --> S3
    V4 --> S4
    V5 --> S5

    S2 --> A1
    S3 --> A2
    S4 --> A3
    S5 --> A4
```

#### 视频监控技术架构
```mermaid
sequenceDiagram
    participant C as 摄像头
    participant S as 流媒体服务器
    participant A as 应用服务器
    participant B as 浏览器
    participant AI as AI分析服务

    C->>S: 1. RTSP视频流
    S->>S: 2. FFmpeg转码
    S->>A: 3. HLS流URL
    A->>B: 4. 返回播放地址
    B->>S: 5. 请求HLS流
    S->>B: 6. 返回视频片段

    S->>AI: 7. 视频帧数据
    AI->>AI: 8. 人员识别分析
    AI->>A: 9. 分析结果
    A->>A: 10. 存储统计数据
```

### 预约管理模块
```mermaid
graph TB
    subgraph "预约管理功能"
        R1[预约申请]
        R2[冲突检测]
        R3[审批流程]
        R4[使用跟踪]
        R5[评价反馈]
        R6[统计分析]
    end

    subgraph "业务规则"
        B1[时间规则]
        B2[权限规则]
        B3[资源规则]
        B4[审批规则]
    end

    subgraph "状态管理"
        S1[待审核]
        S2[已批准]
        S3[已拒绝]
        S4[使用中]
        S5[已完成]
        S6[已取消]
    end

    R1 --> B1
    R2 --> B2
    R3 --> B3
    R4 --> B4

    B1 --> S1
    B2 --> S2
    B3 --> S3
    B4 --> S4

    S1 --> S2
    S2 --> S4
    S4 --> S5
    S1 --> S3
    S2 --> S6
```

#### 预约管理业务流程
| 阶段 | 处理逻辑 | 验证规则 | 状态转换 |
|------|---------|---------|---------|
| 申请提交 | 表单验证、时间检查 | 必填项、时间格式、未来时间 | 无 → 待审核 |
| 冲突检测 | 时间重叠检查 | 同资源时间不重叠 | 待审核 → 已拒绝 |
| 权限验证 | 用户权限检查 | 角色权限、资源权限 | 待审核 → 已拒绝 |
| 审批处理 | 人工/自动审批 | 审批规则、业务规则 | 待审核 → 已批准 |
| 使用执行 | 时间到达自动开始 | 预约时间、资源状态 | 已批准 → 使用中 |
| 使用结束 | 时间到达自动结束 | 结束时间、手动结束 | 使用中 → 已完成 |

## 📊 性能优化策略

### 数据库优化
```mermaid
graph TB
    subgraph "查询优化"
        Q1[索引优化]
        Q2[SQL优化]
        Q3[查询缓存]
        Q4[分页优化]
    end

    subgraph "存储优化"
        S1[表分区]
        S2[数据压缩]
        S3[归档策略]
        S4[冷热分离]
    end

    subgraph "连接优化"
        C1[连接池]
        C2[读写分离]
        C3[负载均衡]
        C4[故障转移]
    end

    subgraph "缓存策略"
        Cache1[Redis缓存]
        Cache2[应用缓存]
        Cache3[查询缓存]
        Cache4[页面缓存]
    end

    Q1 --> Cache1
    Q2 --> Cache2
    S1 --> C1
    S2 --> C2
    C3 --> Cache3
    C4 --> Cache4
```

### 应用性能优化
| 优化层面 | 优化策略 | 技术方案 | 预期效果 |
|---------|---------|---------|---------|
| 前端优化 | 资源压缩、CDN加速 | Gzip、静态资源CDN | 页面加载提升50% |
| 接口优化 | 异步处理、批量操作 | 线程池、批处理 | 响应时间减少30% |
| 数据库优化 | 索引优化、查询优化 | 复合索引、SQL调优 | 查询性能提升60% |
| 缓存优化 | 多级缓存、缓存预热 | Redis、本地缓存 | 缓存命中率90%+ |
| 网络优化 | 连接复用、压缩传输 | Keep-Alive、Gzip | 网络传输减少40% |

### 系统监控指标
```mermaid
graph TB
    subgraph "性能监控"
        P1[响应时间]
        P2[吞吐量]
        P3[并发数]
        P4[错误率]
    end

    subgraph "资源监控"
        R1[CPU使用率]
        R2[内存使用率]
        R3[磁盘I/O]
        R4[网络带宽]
    end

    subgraph "业务监控"
        B1[用户活跃度]
        B2[功能使用率]
        B3[设备在线率]
        B4[告警数量]
    end

    subgraph "告警机制"
        A1[阈值告警]
        A2[趋势告警]
        A3[异常告警]
        A4[业务告警]
    end

    P1 --> A1
    P2 --> A1
    R1 --> A2
    R2 --> A2
    B1 --> A3
    B2 --> A4
```

## 🔐 安全架构设计

### 安全防护体系
```mermaid
graph TB
    subgraph "网络安全"
        N1[防火墙]
        N2[入侵检测]
        N3[DDoS防护]
        N4[VPN接入]
    end

    subgraph "应用安全"
        A1[身份认证]
        A2[权限控制]
        A3[输入验证]
        A4[输出编码]
        A5[会话管理]
    end

    subgraph "数据安全"
        D1[数据加密]
        D2[传输加密]
        D3[备份加密]
        D4[访问审计]
    end

    subgraph "运维安全"
        O1[安全配置]
        O2[漏洞扫描]
        O3[安全更新]
        O4[应急响应]
    end

    N1 --> A1
    N2 --> A2
    A3 --> D1
    A4 --> D2
    D3 --> O1
    D4 --> O2
```

### 安全技术实现
| 安全领域 | 技术方案 | 实现细节 | 安全等级 |
|---------|---------|---------|---------|
| 身份认证 | 多因子认证 | 密码+验证码+生物识别 | 高 |
| 数据加密 | AES-256 | 数据库字段级加密 | 高 |
| 传输安全 | TLS 1.3 | HTTPS强制加密 | 高 |
| 访问控制 | RBAC+ABAC | 细粒度权限控制 | 中 |
| 审计日志 | 全量记录 | 操作、访问、异常日志 | 中 |
| 漏洞防护 | WAF+IDS | Web应用防火墙 | 中 |

## 🚀 技术实施方案

### 开发环境配置
```mermaid
graph TB
    subgraph "开发工具链"
        IDE[IntelliJ IDEA]
        Git[Git版本控制]
        Maven[Maven构建工具]
        Docker[Docker容器]
    end

    subgraph "开发环境"
        JDK[JDK 8+]
        Tomcat[Tomcat 9]
        MySQL[MySQL 8.0]
        Node[Node.js 16+]
    end

    subgraph "测试工具"
        JUnit[JUnit 5]
        Mockito[Mockito]
        Selenium[Selenium]
        JMeter[JMeter]
    end

    subgraph "代码质量"
        SonarQube[SonarQube]
        Checkstyle[Checkstyle]
        SpotBugs[SpotBugs]
        PMD[PMD]
    end

    IDE --> JDK
    Git --> Maven
    Maven --> Docker

    JDK --> JUnit
    Tomcat --> Mockito
    MySQL --> Selenium
    Node --> JMeter

    JUnit --> SonarQube
    Mockito --> Checkstyle
    Selenium --> SpotBugs
    JMeter --> PMD
```

### 部署流程
```mermaid
flowchart TD
    A[代码提交] --> B[自动构建]
    B --> C[单元测试]
    C --> D{测试通过?}
    D -->|否| E[构建失败通知]
    D -->|是| F[代码质量检查]
    F --> G{质量达标?}
    G -->|否| H[质量问题通知]
    G -->|是| I[打包部署包]
    I --> J[部署到测试环境]
    J --> K[集成测试]
    K --> L{测试通过?}
    L -->|否| M[测试失败通知]
    L -->|是| N[部署到预生产]
    N --> O[性能测试]
    O --> P{性能达标?}
    P -->|否| Q[性能问题通知]
    P -->|是| R[部署到生产环境]
    R --> S[健康检查]
    S --> T[部署完成]

    E --> U[修复问题]
    H --> U
    M --> U
    Q --> U
    U --> A
```

### 监控告警体系
```mermaid
graph TB
    subgraph "数据采集"
        C1[应用指标]
        C2[系统指标]
        C3[业务指标]
        C4[日志数据]
    end

    subgraph "数据处理"
        P1[Prometheus]
        P2[Grafana]
        P3[ELK Stack]
        P4[AlertManager]
    end

    subgraph "告警通知"
        N1[邮件通知]
        N2[短信通知]
        N3[微信通知]
        N4[钉钉通知]
    end

    subgraph "响应处理"
        R1[自动恢复]
        R2[人工处理]
        R3[故障转移]
        R4[应急预案]
    end

    C1 --> P1
    C2 --> P1
    C3 --> P2
    C4 --> P3

    P1 --> P4
    P2 --> P4
    P3 --> P4

    P4 --> N1
    P4 --> N2
    P4 --> N3
    P4 --> N4

    N1 --> R1
    N2 --> R2
    N3 --> R3
    N4 --> R4
```

## 📈 项目管理与质量保证

### 开发流程管理
| 阶段 | 活动 | 交付物 | 质量标准 |
|------|------|--------|---------|
| 需求分析 | 需求调研、分析、评审 | 需求规格说明书 | 需求覆盖率100% |
| 系统设计 | 架构设计、详细设计 | 设计文档、原型 | 设计评审通过 |
| 编码实现 | 代码开发、单元测试 | 源代码、测试用例 | 代码覆盖率80%+ |
| 集成测试 | 功能测试、性能测试 | 测试报告 | 缺陷密度<3个/KLOC |
| 系统测试 | 系统测试、用户验收 | 测试报告、验收报告 | 用户满意度90%+ |
| 部署上线 | 部署、培训、维护 | 部署文档、培训材料 | 系统可用性99.9% |

### 代码质量标准
```mermaid
graph TB
    subgraph "代码规范"
        S1[命名规范]
        S2[注释规范]
        S3[格式规范]
        S4[结构规范]
    end

    subgraph "质量指标"
        Q1[代码覆盖率 > 80%]
        Q2[圈复杂度 < 10]
        Q3[重复代码率 < 3%]
        Q4[技术债务 < 5%]
    end

    subgraph "检查工具"
        T1[SonarQube]
        T2[Checkstyle]
        T3[PMD]
        T4[SpotBugs]
    end

    subgraph "持续改进"
        I1[代码评审]
        I2[重构优化]
        I3[技术分享]
        I4[最佳实践]
    end

    S1 --> Q1
    S2 --> Q2
    S3 --> Q3
    S4 --> Q4

    Q1 --> T1
    Q2 --> T2
    Q3 --> T3
    Q4 --> T4

    T1 --> I1
    T2 --> I2
    T3 --> I3
    T4 --> I4
```

---

## 📞 技术支持与联系方式

### 技术团队
- **技术架构师**: 负责系统架构设计和技术选型
- **后端开发工程师**: 负责Java后端开发和数据库设计
- **前端开发工程师**: 负责Web前端开发和用户体验
- **运维工程师**: 负责系统部署、监控和维护
- **测试工程师**: 负责功能测试、性能测试和质量保证

### 文档维护
本技术文档将持续更新，反映系统的最新技术架构和实现细节。如有技术问题或建议，请联系技术团队。

**文档版本**: v2.0.0
**最后更新**: 2025年6月24日
**维护团队**: 产教融合大楼数字化管理系统技术团队

## 🏗️ 系统架构图详解

### 网络拓扑图
```mermaid
graph TB
    subgraph "外网"
        Internet[互联网]
        CDN[CDN服务]
    end

    subgraph "DMZ区"
        LB[负载均衡器]
        Firewall[防火墙]
        Proxy[反向代理]
    end

    subgraph "应用服务器集群"
        Web1[Web服务器1<br/>Tomcat 9]
        Web2[Web服务器2<br/>Tomcat 9]
        Web3[Web服务器3<br/>Tomcat 9]
    end

    subgraph "数据库集群"
        DB_Master[(MySQL主库)]
        DB_Slave1[(MySQL从库1)]
        DB_Slave2[(MySQL从库2)]
        Redis[(Redis缓存)]
    end

    subgraph "文件存储"
        NFS[NFS文件服务器]
        Backup[备份服务器]
    end

    subgraph "监控服务"
        Monitor[监控服务器]
        Log[日志服务器]
    end

    subgraph "外部设备"
        Camera1[摄像头1]
        Camera2[摄像头2]
        IoT1[IoT设备1]
        IoT2[IoT设备2]
        StreamServer[流媒体服务器]
    end

    Internet --> CDN
    CDN --> Firewall
    Firewall --> LB
    LB --> Proxy

    Proxy --> Web1
    Proxy --> Web2
    Proxy --> Web3

    Web1 --> DB_Master
    Web2 --> DB_Master
    Web3 --> DB_Master

    DB_Master --> DB_Slave1
    DB_Master --> DB_Slave2

    Web1 --> Redis
    Web2 --> Redis
    Web3 --> Redis

    Web1 --> NFS
    Web2 --> NFS
    Web3 --> NFS

    NFS --> Backup

    Web1 --> Monitor
    Web2 --> Monitor
    Web3 --> Monitor

    Monitor --> Log

    Web1 --> Camera1
    Web1 --> Camera2
    Web2 --> IoT1
    Web2 --> IoT2
    Web3 --> StreamServer
```

### 模块关系图
```mermaid
graph TB
    subgraph "前端模块"
        UI1[用户界面模块]
        UI2[楼层布局模块]
        UI3[设备控制模块]
        UI4[监控显示模块]
        UI5[统计图表模块]
    end

    subgraph "控制器模块"
        C1[用户控制器]
        C2[房间控制器]
        C3[设备控制器]
        C4[摄像头控制器]
        C5[预约控制器]
        C6[统计控制器]
    end

    subgraph "服务层模块"
        S1[用户服务]
        S2[房间服务]
        S3[设备服务]
        S4[视频流服务]
        S5[预约服务]
        S6[统计服务]
        S7[通知服务]
        S8[安全服务]
    end

    subgraph "数据访问模块"
        D1[用户DAO]
        D2[房间DAO]
        D3[设备DAO]
        D4[摄像头DAO]
        D5[预约DAO]
        D6[日志DAO]
    end

    subgraph "工具模块"
        U1[数据库工具]
        U2[文件工具]
        U3[网络工具]
        U4[加密工具]
        U5[日志工具]
        U6[配置工具]
    end

    subgraph "外部接口模块"
        E1[设备接口]
        E2[摄像头接口]
        E3[流媒体接口]
        E4[第三方API接口]
    end

    UI1 --> C1
    UI2 --> C2
    UI3 --> C3
    UI4 --> C4
    UI5 --> C6

    C1 --> S1
    C2 --> S2
    C3 --> S3
    C4 --> S4
    C5 --> S5
    C6 --> S6

    S1 --> D1
    S2 --> D2
    S3 --> D3
    S4 --> D4
    S5 --> D5
    S6 --> D6

    S1 --> S8
    S2 --> S7
    S3 --> S7
    S4 --> S7
    S5 --> S7

    D1 --> U1
    D2 --> U1
    D3 --> U1
    D4 --> U1
    D5 --> U1
    D6 --> U1

    S3 --> E1
    S4 --> E2
    S4 --> E3
    S6 --> E4

    S8 --> U4
    S7 --> U5
    S1 --> U6
```

### 部署架构图
```mermaid
graph TB
    subgraph "生产环境"
        subgraph "Web层"
            Nginx[Nginx反向代理]
            Tomcat1[Tomcat实例1]
            Tomcat2[Tomcat实例2]
        end

        subgraph "应用层"
            App1[应用服务器1<br/>192.168.1.10]
            App2[应用服务器2<br/>192.168.1.11]
        end

        subgraph "数据层"
            MySQL_M[MySQL主库<br/>************]
            MySQL_S[MySQL从库<br/>************]
            Redis_C[Redis集群<br/>************-32]
        end

        subgraph "存储层"
            NFS_Server[NFS存储<br/>************]
            Backup_Server[备份服务器<br/>************]
        end
    end

    subgraph "测试环境"
        Test_Web[测试Web服务器<br/>************]
        Test_DB[测试数据库<br/>************]
    end

    subgraph "开发环境"
        Dev_Local[开发本地环境<br/>localhost]
        Dev_DB[开发数据库<br/>************]
    end

    subgraph "监控环境"
        Prometheus[Prometheus<br/>************]
        Grafana[Grafana<br/>************]
        ELK[ELK Stack<br/>************-54]
    end

    Nginx --> Tomcat1
    Nginx --> Tomcat2
    Tomcat1 --> App1
    Tomcat2 --> App2

    App1 --> MySQL_M
    App2 --> MySQL_M
    MySQL_M --> MySQL_S

    App1 --> Redis_C
    App2 --> Redis_C

    App1 --> NFS_Server
    App2 --> NFS_Server
    NFS_Server --> Backup_Server

    App1 --> Prometheus
    App2 --> Prometheus
    Prometheus --> Grafana

    App1 --> ELK
    App2 --> ELK
```

## 🔧 功能模块详解

### 用户管理模块
```mermaid
graph TB
    subgraph "用户管理功能"
        A1[用户注册]
        A2[用户登录]
        A3[密码管理]
        A4[权限管理]
        A5[用户信息维护]
        A6[用户状态管理]
    end

    subgraph "技术实现"
        B1[Session管理]
        B2[密码加密]
        B3[权限验证]
        B4[RBAC模型]
        B5[用户缓存]
        B6[审计日志]
    end

    subgraph "数据结构"
        C1[User实体]
        C2[Role实体]
        C3[Permission实体]
        C4[UserSession实体]
    end

    A1 --> B2
    A2 --> B1
    A3 --> B2
    A4 --> B4
    A5 --> B5
    A6 --> B6

    B1 --> C4
    B2 --> C1
    B4 --> C2
    B4 --> C3
```

#### 用户管理技术特性
| 特性 | 技术实现 | 安全级别 | 备注 |
|------|---------|---------|------|
| 密码加密 | BCrypt算法 | 高 | 加盐哈希，防彩虹表攻击 |
| 会话管理 | HttpSession + Redis | 中 | 分布式会话，支持集群 |
| 权限控制 | RBAC模型 | 高 | 基于角色的访问控制 |
| 登录保护 | 验证码 + 限流 | 高 | 防暴力破解 |
| 审计日志 | 数据库记录 | 中 | 完整的操作记录 |

### 设备管理模块
```mermaid
graph TB
    subgraph "设备管理功能"
        D1[设备注册]
        D2[设备监控]
        D3[设备控制]
        D4[参数管理]
        D5[告警管理]
        D6[维护管理]
    end

    subgraph "通信协议"
        E1[HTTP/HTTPS]
        E2[MQTT]
        E3[Modbus TCP]
        E4[SNMP]
        E5[WebSocket]
    end

    subgraph "数据处理"
        F1[实时数据采集]
        F2[数据验证]
        F3[数据存储]
        F4[数据分析]
        F5[告警判断]
        F6[报表生成]
    end

    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E4
    D5 --> E5

    E1 --> F1
    E2 --> F1
    E3 --> F2
    E4 --> F3
    E5 --> F4

    F1 --> F5
    F2 --> F5
    F3 --> F6
    F4 --> F6
```

#### 设备管理技术规格
| 功能模块 | 技术方案 | 性能指标 | 扩展性 |
|---------|---------|---------|--------|
| 设备发现 | 网络扫描 + 协议探测 | 1000设备/分钟 | 支持插件扩展 |
| 数据采集 | 多线程 + 连接池 | 10000点/秒 | 水平扩展 |
| 实时监控 | WebSocket推送 | <100ms延迟 | 支持集群 |
| 历史数据 | 时序数据库 | 1年数据 | 分区存储 |
| 告警处理 | 规则引擎 | <1秒响应 | 规则可配置 |

### 视频监控模块
```mermaid
graph TB
    subgraph "视频监控功能"
        V1[摄像头管理]
        V2[视频流处理]
        V3[录像管理]
        V4[人员识别]
        V5[告警联动]
        V6[存储管理]
    end

    subgraph "流媒体技术"
        S1[RTSP协议]
        S2[FFmpeg转码]
        S3[HLS流媒体]
        S4[WebRTC]
        S5[流媒体服务器]
    end

    subgraph "AI算法"
        A1[人脸检测]
        A2[人数统计]
        A3[行为分析]
        A4[异常检测]
    end

    V1 --> S1
    V2 --> S2
    V3 --> S3
    V4 --> S4
    V5 --> S5

    S2 --> A1
    S3 --> A2
    S4 --> A3
    S5 --> A4
```

#### 视频监控技术架构
```mermaid
sequenceDiagram
    participant C as 摄像头
    participant S as 流媒体服务器
    participant A as 应用服务器
    participant B as 浏览器
    participant AI as AI分析服务

    C->>S: 1. RTSP视频流
    S->>S: 2. FFmpeg转码
    S->>A: 3. HLS流URL
    A->>B: 4. 返回播放地址
    B->>S: 5. 请求HLS流
    S->>B: 6. 返回视频片段

    S->>AI: 7. 视频帧数据
    AI->>AI: 8. 人员识别分析
    AI->>A: 9. 分析结果
    A->>A: 10. 存储统计数据
```

## 📊 性能优化策略

### 数据库优化
```mermaid
graph TB
    subgraph "查询优化"
        Q1[索引优化]
        Q2[SQL优化]
        Q3[查询缓存]
        Q4[分页优化]
    end

    subgraph "存储优化"
        S1[表分区]
        S2[数据压缩]
        S3[归档策略]
        S4[冷热分离]
    end

    subgraph "连接优化"
        C1[连接池]
        C2[读写分离]
        C3[负载均衡]
        C4[故障转移]
    end

    subgraph "缓存策略"
        Cache1[Redis缓存]
        Cache2[应用缓存]
        Cache3[查询缓存]
        Cache4[页面缓存]
    end

    Q1 --> Cache1
    Q2 --> Cache2
    S1 --> C1
    S2 --> C2
    C3 --> Cache3
    C4 --> Cache4
```

### 应用性能优化
| 优化层面 | 优化策略 | 技术方案 | 预期效果 |
|---------|---------|---------|---------|
| 前端优化 | 资源压缩、CDN加速 | Gzip、静态资源CDN | 页面加载提升50% |
| 接口优化 | 异步处理、批量操作 | 线程池、批处理 | 响应时间减少30% |
| 数据库优化 | 索引优化、查询优化 | 复合索引、SQL调优 | 查询性能提升60% |
| 缓存优化 | 多级缓存、缓存预热 | Redis、本地缓存 | 缓存命中率90%+ |
| 网络优化 | 连接复用、压缩传输 | Keep-Alive、Gzip | 网络传输减少40% |

### 系统监控指标
```mermaid
graph TB
    subgraph "性能监控"
        P1[响应时间]
        P2[吞吐量]
        P3[并发数]
        P4[错误率]
    end

    subgraph "资源监控"
        R1[CPU使用率]
        R2[内存使用率]
        R3[磁盘I/O]
        R4[网络带宽]
    end

    subgraph "业务监控"
        B1[用户活跃度]
        B2[功能使用率]
        B3[设备在线率]
        B4[告警数量]
    end

    subgraph "告警机制"
        A1[阈值告警]
        A2[趋势告警]
        A3[异常告警]
        A4[业务告警]
    end

    P1 --> A1
    P2 --> A1
    R1 --> A2
    R2 --> A2
    B1 --> A3
    B2 --> A4
```

## 🔐 安全架构设计

### 安全防护体系
```mermaid
graph TB
    subgraph "网络安全"
        N1[防火墙]
        N2[入侵检测]
        N3[DDoS防护]
        N4[VPN接入]
    end

    subgraph "应用安全"
        A1[身份认证]
        A2[权限控制]
        A3[输入验证]
        A4[输出编码]
        A5[会话管理]
    end

    subgraph "数据安全"
        D1[数据加密]
        D2[传输加密]
        D3[备份加密]
        D4[访问审计]
    end

    subgraph "运维安全"
        O1[安全配置]
        O2[漏洞扫描]
        O3[安全更新]
        O4[应急响应]
    end

    N1 --> A1
    N2 --> A2
    A3 --> D1
    A4 --> D2
    D3 --> O1
    D4 --> O2
```

### 安全技术实现
| 安全领域 | 技术方案 | 实现细节 | 安全等级 |
|---------|---------|---------|---------|
| 身份认证 | 多因子认证 | 密码+验证码+生物识别 | 高 |
| 数据加密 | AES-256 | 数据库字段级加密 | 高 |
| 传输安全 | TLS 1.3 | HTTPS强制加密 | 高 |
| 访问控制 | RBAC+ABAC | 细粒度权限控制 | 中 |
| 审计日志 | 全量记录 | 操作、访问、异常日志 | 中 |
| 漏洞防护 | WAF+IDS | Web应用防火墙 | 中 |
