# 楼层布局图功能说明

## 功能概述
产教融合大楼楼层布局图是一个交互式的2D楼层平面图页面，用户可以通过点击房间查看详细信息。

## 访问方式
1. **主页面访问**：在主页面点击"楼层布局图"卡片
2. **直接访问**：通过URL `http://localhost:8080/EduFusionCenter/layout/floor1` 或 `http://localhost:8080/EduFusionCenter/floor-layout.jsp`

## 功能特性
- **交互式布局**：点击房间区域查看详细信息
- **房间信息显示**：显示房间号、类型、描述等信息
- **房间类型分类**：支持多种房间类型，包括：
  - 研发中心（紫色）
  - 教室（蓝色）
  - 实验室（绿色）
  - 办公室（橙色）
  - 会议室（红色）
  - 多媒体教室（青色）
  - 储藏室（灰色）
  - 公共区域（浅蓝色）
  - 公共设施（粉色）
  - 通道（黄色）
- **美观设计**：现代化UI设计，响应式布局

## 技术实现
- **后端**：Spring MVC控制器 `FloorLayoutController`
- **前端**：JSP页面 + JavaScript + CSS
- **页面文件**：
  - `floor1.jsp`：通过控制器访问的页面
  - `floor-layout.jsp`：独立访问页面

## 数据结构
房间数据包含以下字段：
- `roomNumber`：房间号
- `roomType`：房间类型
- `description`：房间描述
- `x`, `y`：房间在布局图中的位置坐标
- `width`, `height`：房间的宽度和高度

## 文件结构
```
EduFusionCenter/
├── src/main/java/com/edufusion/controller/
│   └── FloorLayoutController.java
├── src/main/webapp/WEB-INF/views/
│   ├── dashboard/
│   │   └── index.jsp
│   └── layout/
│       └── floor1.jsp
└── src/main/webapp/
    └── floor-layout.jsp
```

## 使用说明
1. 访问楼层布局图页面
2. 查看整体楼层布局
3. 点击任意房间区域查看详细信息
4. 右侧面板显示房间类型图例
5. 使用返回按钮回到主页面

## 扩展性
- 可以轻松添加更多楼层
- 支持添加新的房间类型
- 可以集成更多交互功能（如路径规划等）

## 注意事项
- 确保房间坐标和尺寸与实际布局图匹配
- 房间类型样式需要在CSS中定义
- 建议使用相对坐标以适应不同屏幕尺寸

## 更新日志
- **v1.0**：基础楼层布局图功能
- **v1.1**：添加研发中心房间类型支持
- **v1.2**：更新房间数据，增加更多房间类型样式 